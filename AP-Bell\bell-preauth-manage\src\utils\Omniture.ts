// s_oPYM:bank, Credit
// s_oCCDT:credit card type
import{ IBankInfoRes, InputBankAccountDetail, PaymentItem }  from "../models"; // Ensure this path is correct or update it to the correct path

export const cardPatterns = {
  VISA: /^4/,
  MASTERCARD: /^(5[1-5]|2[2-7])/,
  AMEX: /^3[47]/,
};

export const getCardType = (number: string) => {
  if (cardPatterns.VISA.test(number)) return "VISA";
  if (cardPatterns.MASTERCARD.test(number)) return "MASTERCARD";
  if (cardPatterns.AMEX.test(number)) return "AMEX";
  return " "; // Return default for unknown patterns
};
export const IsInteracValueChanged = (bankInfo: IBankInfoRes, bankAccountDetail?: InputBankAccountDetail) => {
  let result = false;
  if(bankInfo.bankAccountNumber != "" && bankAccountDetail && bankAccountDetail.AccountNumber != "")
   {
    if(bankAccountDetail.AccountHolder !== bankInfo.accountHolderName || bankAccountDetail.BankName !== bankInfo.bankCode || bankAccountDetail.TransitNumber !== bankInfo.transitNumber || bankAccountDetail.AccountNumber !== bankInfo.bankAccountNumber )
    {
      result = true;
    }
  }
  return result;
}
const getItems = (item: PaymentItem, count: number) => {
  let newCount = count;
  if (newCount === 0 && (item.BankAccountDetails || item.CreditCardDetails)) {
    newCount = newCount + 1;
    return item;
  }
  return;
};

const getPreauthBanDetails = (checkedBillItems: PaymentItem[], paymentItem?: PaymentItem[]) => {
  let count = 0;
  let filterItems;

  filterItems = checkedBillItems?.length > 0 && checkedBillItems.filter((item: PaymentItem) => {
      return getItems(item, count);
  });

  if (filterItems && filterItems.length === 0 && paymentItem && paymentItem.length > 0) {
    filterItems = paymentItem.filter((item: PaymentItem) => {
       return getItems(item, count);
    });
  }

  return filterItems && filterItems.length > 0 ? filterItems[0] : null;
} 

export const getPaymentSelectOmniture = (
  isError: boolean,
  isBankChecked: boolean,
  checkedBillItems: PaymentItem[],
  isCanceledPreauth?:boolean,
  InteracCode?:string,
  paymentItem?: PaymentItem[]
) => {
  const billItemsOnPreauth = getPreauthBanDetails(checkedBillItems, paymentItem);
  if(isCanceledPreauth){
    return {
      s_oPYM: "Cancel payment",
      error: "",
      s_oAPT: "332-1-0",
    };
  }
  else{
  if (isError) {
    if (Array.isArray(checkedBillItems) && checkedBillItems.length > 0 && checkedBillItems[0]) {
      if (isBankChecked) {
        if (billItemsOnPreauth?.BankAccountDetails) {
          return {
            s_oPYM: "Bank payment",
            error: "BANKERROR",
            s_oAPT: "327-2-2",
            s_oILI: InteracCode
          };
        } else if (billItemsOnPreauth?.CreditCardDetails) {
          return {
            s_oPYM: "Bank payment",
            error: "BANKERROR",
            s_oAPT: "331-2-2",
            s_oILI: InteracCode
          };
        }
      } else {
        if (billItemsOnPreauth?.BankAccountDetails) {
          return {
            s_oPYM: "Credit card",
            error: "CREDITERROR",
            s_oAPT: "328-2-2",
            s_oILI: InteracCode
          };
        } else if (billItemsOnPreauth?.CreditCardDetails) {
          return {
            s_oPYM: "Credit card",
            error: "CREDITERROR",
            s_oAPT: "330-2-2",
            s_oILI: InteracCode
          };
        }
      }
    }
  } else {
    if (Array.isArray(checkedBillItems) && checkedBillItems.length > 0 && checkedBillItems[0]) {
      if (isBankChecked) {
        if (billItemsOnPreauth?.BankAccountDetails) {
          return {
            s_oPYM: "Bank payment",
            error: "",
            s_oAPT: "327-1-0",
            s_oILI: InteracCode
          };
        } else if (billItemsOnPreauth?.CreditCardDetails) {
          return {
            s_oPYM: "Bank payment",
            error: "",
            s_oAPT: "331-1-0",
            s_oILI: InteracCode
          };
        }
      } else {
        if (Array.isArray(checkedBillItems) && checkedBillItems.length > 0 && checkedBillItems[0]) {
          if (billItemsOnPreauth?.BankAccountDetails) {
          return {
            s_oPYM: "Credit card",
            error: "",
            s_oAPT: "328-1-0",
            s_oILI: InteracCode
          };
          } else if (billItemsOnPreauth?.CreditCardDetails) {
          return {
            s_oPYM: "Credit card",
            error: "",
            s_oAPT: "330-1-0",
            s_oILI: InteracCode
          };
          }
        }
        
      }
    }
  }
  }
  return {
    s_oPYM: "",
    error: "",
    s_oAPT: "",
    s_oILI: ""  
  };
};


export const getConfirmationOmniture = (
  isBankpaymentSelscted: boolean,
  checkedBillItems: PaymentItem[],
  submitMultiOrderPayment: any,
  CreditCardType?: string,
  paymentItem?: PaymentItem[],
  InteracCode?:string

) => {
  if (Array.isArray(checkedBillItems) && checkedBillItems.length > 0 && checkedBillItems[0]) {
    if (isBankpaymentSelscted) {
      if (checkedBillItems[0].BankAccountDetails) {
        return {
          s_oPYM: "Bank payment",
          s_oAPT: "327-2-1",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      } else if (checkedBillItems[0].CreditCardDetails) {
        return {
          s_oPYM: "Bank payment",
          s_oAPT: "331-2-1",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      }
    } else if (!isBankpaymentSelscted) {
      if (checkedBillItems[0].BankAccountDetails) {
        return {
          s_oPYM: "Credit card",
          s_oAPT: "328-2-1",
          s_oCCDT: { CreditCardType },
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
        } else if (checkedBillItems[0].CreditCardDetails) {
        return {
          s_oPYM: "Credit card",
          s_oAPT: "330-2-1",
          s_oCCDT: { CreditCardType },
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      }
    }
}
  return {
    s_oPYM: "",
    s_oCCDT: "",
  };
};

export const getOmnitureOnConfirmationFailure = (
  isBankpaymentSelscted: boolean,
  checkedBillItems: PaymentItem[],
  CreditCardType?: any,
  paymentItem?: PaymentItem[]

) => {
  if (Array.isArray(checkedBillItems) && checkedBillItems.length > 0 && checkedBillItems[0]) {
    if (isBankpaymentSelscted) {
      if (checkedBillItems[0].BankAccountDetails) {
        return {
          s_oPYM: "Bank payment",
          s_oAPT: "327-2-2",
        };
      } else if (checkedBillItems[0].CreditCardDetails) {
        return {
          s_oPYM: "Bank payment",
          s_oAPT: "331-2-2",
        };
      }
    } else if (!isBankpaymentSelscted) {
      if (checkedBillItems[0].BankAccountDetails) {
        return {
          s_oPYM: "Credit card",
          s_oAPT: "328-2-2",
          s_oCCDT: { CreditCardType },
        };
      } else if (checkedBillItems[0].CreditCardDetails) {
        return {
          s_oPYM: "Credit card",
          s_oAPT: "330-2-2",
          s_oCCDT: { CreditCardType },
        };
      }
    }
  }
  

  return {
    s_oPYM: "",
    s_oAPT: "",
    s_oCCDT: "",
  };

};

export const getOmnitureOnFailure = (
 isBankpaymentSelscted: boolean,
  checkedBillItems: PaymentItem[],
  CreditCardType?: any,
  paymentItem?: PaymentItem[]
) => {
  if (Array.isArray(checkedBillItems) && checkedBillItems.length > 0 && checkedBillItems[0]) {
    if (isBankpaymentSelscted) {
      if (checkedBillItems[0].BankAccountDetails) {
        return {
          s_oPYM: "Bank payment",
          s_oAPT: "327-2-2",
        };
      } else if (checkedBillItems[0].CreditCardDetails) {
        return {
          s_oPYM: "Bank payment",
          s_oAPT: "331-2-2",
        };
      }
    } else if (!isBankpaymentSelscted) {
      if (checkedBillItems[0].BankAccountDetails) {
        return {
          s_oPYM: "Credit card",
          s_oAPT: "328-2-2",
          s_oCCDT: { CreditCardType },
        };
      } else if (checkedBillItems[0].CreditCardDetails) {
        return {
          s_oPYM: "Credit card",
          s_oAPT: "330-2-2",
          s_oCCDT: { CreditCardType },
        };
      }
    }
  }
 

  return {
    s_oPYM: "",
    s_oAPT: "",
    s_oCCDT: "",
  };

}

export const IsDetailsValid = (validateMultiOrderPayment: any) => {
  let result = false ;
  if (Object.values(validateMultiOrderPayment).length > 0) {
    for (let item of Object.values(validateMultiOrderPayment) as any) {
      if (item?.errorCodeID && item?.errorCodeID !== "") {
        result = true;
        break;
      }
    }
  }
  return result;
}
