import React, { useState } from "react";
import { injectIntl } from "react-intl";
import { FormControl, Text, InputText, Label, FormGroup, Select, SelectOption} from "@bell/bell-ui-library";
import { DivProps } from "@bell/bell-ui-library/dist/@types/src/_types";
import { LightBoxNoname, LightBoxSecurityCode } from "../LightBox";


interface PaymentAlreadyExistProps extends DivProps  {
  intl: any;
  isCreditCardPayment?: boolean;
  isBankPayment?: boolean;
}

interface ChildProps {
  defaultChecked?: boolean;
  showBankFieldsOnChange?: boolean;
}
// Creditcard Fuctions HERE
const cardPatterns = {
  visa: /^4/,
  mastercard: /^(5[1-5]|2[2-7])/,
  amex: /^3[47]/
};

//Card Type
const getCardType = (number: string) => {
  if (cardPatterns.visa.test(number)) return "visa";
  if (cardPatterns.mastercard.test(number)) return "mastercard";
  if (cardPatterns.amex.test(number)) return "amex";
  return "default"; // Return default for unknown patterns
};

// Creditcard Fuctions UP HERE

export interface FormFieldsCreditCardPaymentProps {
  errorExpiryDate?: boolean;
  intl: any;
}

const numberOnly = (e: any) => {
  e.target.value = e.target.value.replace(/[^0-9]/gi, "");
}

const getMonthList = () => {
  return Array.from({ length: 12 }, (_, i) => {
      let month: string | number = i + 1;
      if (i < 9) {
      month = i + 1;
      month = "0" + month;
      }
      return month;
  });
}

const getYearList = () => {
  let max = parseInt(new Date().getFullYear().toString().substring(2));
  let min = max + 9;
  let years = [];
  for (let i = max; i <= min; i++) {
      years.push(i);
  }
  return years;   
}

const PaymentAlreadyExistComponent = ({  intl, isCreditCardPayment = false,isBankPayment = false, children}: PaymentAlreadyExistProps) => {
  const getInitialState = () => {
    let initialState = false;
    React.Children.forEach(children, (child) => {
      if (React.isValidElement<ChildProps>(child) && child.props.defaultChecked && child.props.showBankFieldsOnChange) {
        initialState = true;
      }
    });
    return initialState;
  };

  const [ bankPaymentShown, setBankPaymentShown ] = useState(getInitialState);
  const handleRadioChange = (isShown: boolean) => {
    setBankPaymentShown(isShown); 
  };

  const legendStr = isCreditCardPayment
    ? intl.formatMessage({id:"EXISTING_CC_TITLE"})
    : isBankPayment
    ? intl.formatMessage({id:"EXISTING_BANK_TITLE"})
    : null;

    const [cardNumber, setCardNumber] = React.useState("");
    const [cardType, setCardType] = React.useState("default");
    const [CVV, setCVV] = React.useState('');
    const [errorCardNumber] = React.useState(false);
    const [errorCardName] = React.useState(false);
    const [errorSecurityCode] = React.useState(false);
    const [errorExpiryDate] = React.useState(false);
    
    const inputRefs = {
        inputCardNumber: React.useRef(null),
        inputCardName: React.useRef(null),
        inputSecurityCode: React.useRef(null),
    }

    console.log(errorCardNumber);
    console.log(errorCardName);
    console.log(errorSecurityCode);

    const handleInputChange = (e:any) => {
        const input = e.target.value;
        setCardNumber(input);
        // card type here
        const detectedCardType = getCardType(input);
        setCardType(detectedCardType);
    };
    
    const handleMaskCVV = (e: any) => {
        setCVV(e.target.value);
    }

    const cardIcons = {
        visa: intl.formatMessage({id:"VISA_CC_PNG"}),
        mastercard: intl.formatMessage({id:"MASTER_CC_PNG"}),
        amex: intl.formatMessage({id:"AMEX_CC_PNG"})
        
    };

  return (
    <>
    <fieldset>
        <legend className="brui-sr-only">{legendStr}</legend>
        <div className="brui-flex brui-flex-col">
          <div className="brui-flex brui-flex-col sm:brui-flex-row">
            <div className="sm:brui-min-w-[174px] sm:brui-mr-30 sm:brui-text-right brui-pt-13 brui-pb-13 sm:brui-pb-0 payment-mb-10 sm:payment-mb-0">
              <Label className="brui-block" required>{legendStr}</Label>
            </div>
            <div className="brui-flex brui-flex-col payment-gap-15">
              {React.Children.map(children, (child, index) => {
                return React.cloneElement(child as React.ReactElement<any>, {
                  showOnChange: handleRadioChange,
                  // hasError: ,
                  childIndex: index
                });
              })}
            </div>
          </div>
          <div className={["", bankPaymentShown ? "brui-block": "brui-hidden", "brui-delay-1000"].join(" ").trim()}>
            {isCreditCardPayment && (
              <>
              <form noValidate>
                  {/* card number */}
                  <FormControl className="sm:brui-flex-row payment-mt-30">
                      <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-20 brui-justify-center sm:brui-h-44 brui-mb-10 sm:brui-mb-0">
                          <Label htmlFor="text-1" isError={errorCardNumber} required={true}>
                              {intl.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"})}
                          </Label>
                          <Text className="brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray">
                              {intl.formatMessage({id:"CREDIT_CARD_NUMBER_DESC_INPUT_LABEL"})}
                          </Text>
                      </div>
                      <div className="brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap">
                          <InputText
                              value={cardNumber}
                              onChange={handleInputChange} 
                              onInput={(e:any) => numberOnly(e)} 
                              className="sm:!payment-w-[280px]" 
                              id="text-1" 
                              required={true} 
                              isError={errorCardNumber} 
                              errorMessage={"This is required field"} 
                              ref={inputRefs.inputCardNumber}
                          />
                          <div className="brui-flex brui-items-center sm:brui-h-44 brui-gap-15 sm:brui-mt-0 payment-ml-0 sm:payment-ml-30 payment-mt-10 sm:payment-mt-0">
                              {Object.entries(cardIcons).map(([type, iconSrc]) => (
                              <img
                                  key={type}
                                  src={iconSrc}
                                  alt={`${type} card`}
                                  className="brui-h-32 payment-mr-15 brui-object-contain"
                                  style={{
                                  opacity: cardType === type ? 1 : 0.5, // Highlight detected card type
                                  transition: "opacity 0.3s ease-in-out",
                                  }}
                              />
                              ))}
                          </div>
                      </div>
                  </FormControl>
                  {/* card name */}
                  <FormControl className="sm:brui-flex-row payment-mt-30">
                      <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-20 brui-justify-center sm:brui-h-44 brui-mb-10 sm:brui-mb-0">
                          <Label htmlFor="text-2" isError={errorCardName} required={true}>
                              {intl.formatMessage({id:"CREDIT_CARD_NAME_LABEL"})}
                          </Label>
                          <Text className="brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray">
                              {intl.formatMessage({id:"CREDIT_CARD_NAME_DESC_INPUT_LABEL"})}
                          </Text>
                      </div>
                      <div className="brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap">
                          <InputText 
                              className="sm:!payment-w-[280px]" 
                              id="text-2" 
                              required={true} 
                              isError={errorCardName} 
                              errorMessage={"This is required field"} 
                              minLength={5}
                              maxLength={70}
                              ref={inputRefs.inputCardName}
                          />
                          <div className="brui-flex brui-items-center sm:brui-h-44 sm:payment-ml-10 payment-mt-5 sm:payment-mt-0">
                              <LightBoxNoname />
                          </div>
                      </div>
                  </FormControl>
                  {/* expiration date */}
                  <FormControl className="sm:brui-flex-row payment-mt-30">
                      <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-20 brui-justify-center sm:brui-h-44 brui-mb-10 sm:brui-mb-0">
                          <Label id="label-3" isError={errorExpiryDate} required={true}>
                              {intl.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"})}
                          </Label>
                      </div>
                      <div className="brui-flex-col">
                      <FormGroup className="brui-flex brui-flex-wrap" hasError={errorExpiryDate} errorMessage="This information is required">
                          <div className="payment-w-[75px]">
                              <Select
                                  name="select-component-3-1"
                                  id="select-1"
                                  aria-labelledby="label-3"
                                  defaultValue=""
                                  placeHolder="MM"
                                  onChange={() => { }}
                                  disableDropdownIcon
                                  className="brui-text-gray"
                              >
                                  {getMonthList().map((item, index) => (
                                  <SelectOption value={item.toString()} id={"option-" + index} displayName={item.toString()} />
                                  ))}
                              </Select>
                          </div>
                          <div className="payment-w-[75px] payment-ml-12">
                              <Select
                                  name="select-component-3-1"
                                  id="select-2"
                                  aria-labelledby="label-3"
                                  defaultValue=""
                                  placeHolder="YY"
                                  onChange={() => { }}
                                  disableDropdownIcon
                                  className="brui-text-gray"
                              >
                                  {getYearList().map((item, index: number) => (
                                  <SelectOption value={item.toString()} id={"option-year-" + index} displayName={item.toString()} />
                                  ))}
                              </Select>
                          </div>
                      </FormGroup>
                      </div>
                  </FormControl>
                  {/* security code */}
                  <FormControl className="sm:brui-flex-row payment-mt-30">
                      <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-20 brui-justify-center sm:brui-h-44 brui-mb-10 sm:brui-mb-0">
                          <Label htmlFor="text-4" isError={errorSecurityCode} required={true}>
                              {intl.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"})}
                          </Label>
                      </div>
                      <div className="brui-flex sm:brui-flex-row brui-flex-wrap">
                          <InputText 
                              onInput={(e:any) => numberOnly(e)} 
                              className="!payment-w-[75px]" 
                              id="text-4" 
                              required={true} 
                              isError={errorSecurityCode} 
                              errorMessage={"This is required field"} 
                              errorMessageClassName="payment-w-[75px] brui-text-nowrap" 
                              type="password"
                              onChange={handleMaskCVV}
                              ref={inputRefs.inputSecurityCode}
                          />
                          <InputText type="hidden" id="text-hidden" value={CVV} />
                          <div className="brui-flex brui-items-center brui-h-44 payment-ml-10">
                              <LightBoxSecurityCode />
                          </div>
                      </div>
                  </FormControl>
              </form>
              <Text className="brui-text-14 brui-leading-18 brui-text-gray payment-mt-45 sm:payment-mt-45 brui-inline-block">
                  {intl.formatMessage({id:"REQUIRED_LABEL"})}
              </Text>
              </>
            )} 
            {isBankPayment && (
              <>
              <FormControl className="sm:brui-flex-row brui-mt-30">
                <div className="sm:brui-w-[174px] sm:brui-min-w-[174px] brui-flex brui-flex-col sm:brui-text-right brui-mr-30 brui-justify-center sm:brui-h-44 brui-mb-10 sm:brui-mb-0">
                  <Label id="select1" htmlFor="select-1"  required={true}>
                    Bank
                  </Label>
                </div>
                <Select
                  className="sm:!brui-w-[280px] brui-text-gray"
                  id="select-1"
                  name="select-component-1"
                  
                  errorMessage={"Please select at least one option"}
                  aria-labelledby="select1"
                >
                  <SelectOption value="BMO" id="option-1" displayName="BMO" />
                  <SelectOption value="CIBC" id="option-2" displayName="CIBC" />
                  <SelectOption value="Desjardins" id="option-3" displayName="Desjardins" />
                  <SelectOption value="National bank" id="option-4" displayName="National bank" />
                  <SelectOption value="RBC" id="option-5" displayName="RBC" />
                  <SelectOption value="Scotiabank" id="option-6" displayName="Scotiabank" />
                  <SelectOption value="TD" id="option-7" displayName="TD" />
                </Select>
              </FormControl>
              
              <FormControl className="sm:brui-flex-row brui-mt-30">
                <div className="sm:brui-w-[174px] sm:brui-min-w-[174px] brui-flex brui-flex-col sm:brui-text-right brui-mr-30 brui-justify-center sm:brui-h-44 brui-mb-10 sm:brui-mb-0">
                  <Label htmlFor="text-2"  required={true}>
                    Account holder name
                  </Label>
                </div>
                <InputText className="sm:!brui-w-[280px] !brui-text-gray" id="text-2" required={true}  errorMessage={"This is required field"}/>
              </FormControl>
        
              <FormControl className="sm:brui-flex-row brui-mt-30">
                <div className="sm:brui-w-[174px] sm:brui-min-w-[174px] brui-flex brui-flex-col sm:brui-text-right brui-mr-30 brui-justify-center sm:brui-h-44 brui-mb-10 sm:brui-mb-0">
                <Label htmlFor="text-3"  required={true}>
                      Transit Number
                    </Label>
                    <Text className="brui-text-12 leading-14 sm:brui-ml-14">
                    5 Digits
                    </Text>
                </div>
                <InputText className="!brui-w-[140px]" id="text-3" required={true}  errorMessage={"This is required field"}/>
              </FormControl>
        
              <FormControl className="sm:brui-flex-row brui-mt-30">
                <div className="sm:brui-w-[174px] sm:brui-min-w-[174px] brui-flex brui-flex-col sm:brui-text-right brui-mr-30 brui-justify-center sm:brui-h-44 brui-mb-10 sm:brui-mb-0">
                <Label htmlFor="text-4"  required={true}>
                      Account Number
                    </Label>
                    <Text className="brui-text-12 leading-14 sm:brui-ml-14">
                    7-12 digits
                    </Text>
                </div>
                <div>
                  <InputText className="!brui-w-[140px]" id="text-4" required={true}  errorMessage={"This is required field"}/>
                  {/* <AccountFetched hasAccountFetched={hasAccountFetched} /> */}
                </div>
              </FormControl>
        
              <Text className="brui-text-14 brui-leading-18 brui-text-gray brui-mt-30 sm:brui-mt-45 brui-inline-block">* Required field</Text>
            </>
            )}
          </div>
        </div>
        {bankPaymentShown}
      </fieldset>
    </>
  );
};

export const PaymentAlreadyExist = injectIntl(PaymentAlreadyExistComponent);