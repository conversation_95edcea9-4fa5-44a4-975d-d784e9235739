
import * as React from "react";
import ErrorPage from "./ErrorPage";
import { injectIntl } from "react-intl";

interface AlertConfirmationSuccessProps {
  intl: any;
}
const APIFailureComponent =  ({ intl }:AlertConfirmationSuccessProps) => (
  <ErrorPage 
    iconVariant="error"
    errorHeaderText={intl.formatMessage({id: "FAILURE_API_BAN_HEADING"})}   
    errorHeaderTextSR={intl.formatMessage({id: "FAILURE_API_BAN_HEADING_SR"})}  
    errorText={<><p>{intl.formatMessage({id: "FAILURE_API_BAN_MAIN_DESC"})}  </p><p className="payment-mt-8">{intl.formatMessage({id: "FAILURE_API_BAN_MAIN_DESC_2"})}  </p></>}
    buttonText={intl.formatMessage({id: "FAILURE_API_BAN_BUTTON"})}  
    suggestionList={{ 
      label: intl.formatMessage({id: "FAILURE_API_BAN_SUB_DESC"}) , 
      items: [
        intl.formatMessage({id: "FAILURE_API_BAN_SUB_DESC_LISTITEM_1"}),
        intl.formatMessage({id: "FAILURE_API_BAN_SUB_DESC_LISTITEM_2"})
      ] 
    }}
    onButtonClick={() => {
      window.location.reload();
    }}
  />
);
export const APIFailure  = injectIntl(APIFailureComponent);
