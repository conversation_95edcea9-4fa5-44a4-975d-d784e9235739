import * as React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, Heading, Text} from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";

interface AlertConfirmationInfoProps {
  intl: any;
}
const AlertConfirmationInfoComponent = ({intl}: AlertConfirmationInfoProps) => (
  <Alert
    variant="info"
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block"
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-12 payment-font-sans payment-font-bold payment-leading-22">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      <p className="payment-text-14 payment-my-15 payment-text-gray">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI"})}
      </p>
      <Text elementType="div" className="sm:payment-flex payment-block">
        <Text elementType="div" className="payment-pr-0 sm:payment-pr-10">
          <Button variant="primary"
            size="regular"
            onClick={() => {
              location.href = `${intl.formatMessage({id: "CTA_MAKE_PAYMENT_LINK"})}`;
            }}
          >
            {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}
          </Button>
        </Text>
      </Text>
    </Text>
  </Alert>
);
export const AlertConfirmationInfo = injectIntl(AlertConfirmationInfoComponent);
