{"name": "bell-preauth-common", "version": "1.0.0", "description": "", "main": "./dist/bell-preauth-common.js", "types": "./dist/@types/index.d.ts", "private": true, "author": "BELL", "license": "MIT", "scripts": {"dev": "webpack -w", "build": "webpack", "build:dev": "webpack --env -d", "build:prod": "webpack --env -p", "build-css": "node-sass ./scss -o ./css"}, "devDependencies": {"bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "husky": "4.3.8"}, "peerDependencies": {"bwtk": "*", "react": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "redux": "*", "redux-actions": "*", "redux-observable": "*", "rxjs": "*"}, "dependencies": {"linklocal": "^2.8.2"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}