import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dalBody, <PERSON>dalContent, ModalHeader} from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import { connect } from "react-redux";
import { OmnitureOnBoxNameLightBox } from "../../store/Actions";
// import ImageTransitAccountNumber from "../../../dev/static"; //../../assets/images/transit-account-number.png
// import ImageInteracVerification from "/interac-verification-v2-en.png";

interface LightboxNonameProps {
  intl: any;
  setOmnitureOnBoxNameLightBox: Function;
}


export const LightBoxNonameComponent = ({intl, setOmnitureOnBoxNameLightBox}:LightboxNonameProps) => {    
  const [isModalOpen, setIsModalOpen] = useState(false);
  const onToggleModal = (isOpen: boolean) => {
    setIsModalOpen(isOpen);
  };
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault(); 
    onToggleModal(true);
    setTimeout(() => {
      setOmnitureOnBoxNameLightBox();
    }, 1000);
  }; 
  return (
    <div>
      <Button 
        variant="textBlue"
        onClick={handleButtonClick}
        size="small"
        className="payment-text-14 payment-leading-18"
      >
        {intl.formatMessage({id: "MODAL_NO_NAME"})}
      </Button>

      {isModalOpen && (
        <Modal
          id="no-name-on-card"
          aria-labelledby="no-name-on-card-title"
          onEscapeKeyPressed={() => onToggleModal(false)}
          onOverlayClick={() => onToggleModal(false)}
        >
          <ModalContent
            useDefaultRadius={false}
            className="payment-rounded-10"
          >
            <ModalHeader
              variant="lightGrayBar"
              rightButtonIcon="default"
              title={intl.formatMessage({id: "MODAL_NO_NAME_TITLE"})}
              isDefaultPadding={false}
              className="payment-px-15 sm:payment-px-30 payment-py-25"
              onRightButtonClicked={() => onToggleModal(false)}
              rightButtonLabel={intl.formatMessage({id: "CTA_CLOSE"})}
            />
            <ModalBody
              isDefaultPadding={false}
              className="payment-px-15 sm:payment-px-30 payment-py-30">
              <div className="payment-text-gray payment-text-14 payment-leading-18">{intl.formatMessage({id: "MODAL_NO_NAME_DESC"})}</div>
            </ModalBody>
          </ModalContent>
        </Modal>
      )}
    </div>
  );
};

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({ 
  setOmnitureOnBoxNameLightBox: (data?:any) => dispatch(OmnitureOnBoxNameLightBox({data}))
});

export const LightBoxNoname = connect(null ,mapDispatchToProps)(injectIntl(LightBoxNonameComponent));

