{"name": "bell-preauth-setup", "version": "1.0.0", "description": "", "main": "dist/widget.js", "private": true, "scripts": {"linklocal": "linklocal", "dev": "webpack -w", "build": "npm run build:tailwind && webpack", "build:dev": "webpack --env -d", "build:prod": "webpack --env -p", "test": "echo \"Error: no test specified\" && exit 1", "start": "http-server --cors ./", "build:tailwind": "npx tailwindcss build -i ./src/css/styles.css -o ./src/css/bell-payment-flow.css --minify", "watch:tailwind": "npx tailwindcss -i ./src/css/styles.css -o ./src/css/bell-payment-flow.css --watch"}, "keywords": [], "author": "BELL", "license": "MIT", "devDependencies": {"@bell/bell-ui-library": "3.7.1", "bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "bell-preauth-common": "file:../bell-preauth-common", "husky": "4.3.8", "webpack-cli": "^5.1.4", "@types/react-router-dom": "*", "css-loader": "^6.2.0", "prettier": "2.3.2", "tailwindcss": "^3.4.1"}, "peerDependencies": {"bwtk": "*", "react": "*", "react-dom": "*", "react-intl": "*", "react-redux": "*", "redux": "*", "redux-actions": "*", "redux-observable": "*", "rxjs": "*"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}