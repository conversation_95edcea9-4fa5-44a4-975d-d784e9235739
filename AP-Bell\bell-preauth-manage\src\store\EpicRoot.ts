// @ts-nocheck
import { Injectable, Store } from "bwtk";

import { Observable, of, from, catchError, map, mergeMap, startWith } from "rxjs";
import { combineEpics, ofType } from "redux-observable";
import { IGetRedirectUrlEpicResponse, IGetRedirectUrlEpic, IInteracBankInfoEpic, IInteracBankInfoEpicResponse, ICancelPreauthPaymentsEpic, ICancelPreauthPaymentsEpicResponse } from "../models";
import {
  tokenizeAndPropagateFormValues,
  getPassKey,
  setPassKey,
  cardTokenizationSuccess,
  // setCreditCardInfo,
  cardTokenizationError,
  getRedirectUrl,
  redirectUrlFailure,
  redirectUrlSuccess,
  getInteracBankInfo,
  interacBankInfoSuccess,
  interacBankInfoFailure,
  setIsLoading,
  createMultiPaymentAction,
  createMultiPaymentCompleted,
  createMultiPaymentFailed,
  validateMultiOrderPaymentAction,
  validateMultiOrderPaymentActionCompleted,
  validateMultiOrderPaymentActionFailed,
  submitMultiOrderPaymentAction,
  submitMultiOrderPaymentActionCompleted,
  submitMultiOrderPaymentActionFailed,
  cancelPreauthAction,
  cancelPreauthSuccessAction,
  cancelPreauthFailureAction
} from "./Actions";

import Tokenize, {
  IDTSTokenizationPluginResponse
} from "../utils/tokenize";

// import Config from "../Config";
import { Client } from "../Client";
import Config from "../Config";


@Injectable
export class EpicRoot {
  constructor(private client: Client, private config: Config) { }
  combineEpics() {
    return combineEpics(
      this.createMultiPaymentEpic,
      this.validateMultiOrderPaymentEpic,
      this.submitMultiOrderPaymentEpic,
      this.tokenizeAndPropagateFormValues,
      this.fetchPassKey,
      this.getRedirectUrl,
      this.getInteracBankInfo,
      this.cancelPreauthPaymentsEpic,
    );
  }

  private get tokenizeAndPropagateFormValues() {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(tokenizeAndPropagateFormValues.toString()),
        mergeMap((action: any) =>
          Tokenize("card-number", this.config.DTSTokenization, store.value.passKey).pipe(
            mergeMap((response: IDTSTokenizationPluginResponse) => [
              getPassKey({ ban: action.payload.BillName, sub: action.payload.subscriberId }),
              cardTokenizationSuccess(response.token)
            ]),
            catchError((err) => of(
              cardTokenizationError(typeof (err) === "string" && err.length > 0 ? err : "TOKENIZATIONERROR")
            ))
          )
        ),
        catchError((err: any) => of(
          cardTokenizationError("TOKENIZATIONERROR")
        ))
      );
  }

  private get fetchPassKey() {
    // const { options } = this.client;
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(getPassKey.toString()),
        // Set the widget staus to loading
        mergeMap((action: any) =>
          from(this.client.getPassKeyRepsonse(action)).pipe(
            map((response: any) => setPassKey(response?.data?.PassKey)),
            catchError(() => of(cardTokenizationError("TOKENIZATIONERROR")))
          )
        )
      );
  }

  private get createMultiPaymentEpic(): any {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(createMultiPaymentAction.toString()),
        mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, details: any, sub?: string | null }>) =>
          from(this.client.createMultiOrderFormData(payload?.ban, payload?.type, payload?.details, payload?.sub)).pipe(
            map(({ data }: any) => createMultiPaymentCompleted(data)),
            catchError((error: any) => of({ ...createMultiPaymentFailed(error), error: true }))
          )
        )
      );
  }

  private get validateMultiOrderPaymentEpic(): any {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(validateMultiOrderPaymentAction.toString()),
        mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, details: any, accountInputValue: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined }>) =>
          from(this.client.validateMultiOrderForm(payload?.ban, payload?.type, payload?.details, payload?.accountInputValue, payload?.isBankPaymentSelected, payload?.sub, store.value.cardTokenizationSuccess)).pipe(
            map(({ data }: any) => validateMultiOrderPaymentActionCompleted(data)),
            catchError((error: any) => of({ ...validateMultiOrderPaymentActionFailed(error), error: true }))
          )
        )
      );
  }

  private get submitMultiOrderPaymentEpic(): any {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(submitMultiOrderPaymentAction.toString()),
        mergeMap(({ payload }: ReduxActions.Action<{ ban: string, type: boolean, isbankSelected: boolean, sorryCredit: boolean, sorryDebit: boolean, details: any, sub?: string | null }>) =>
          from(this.client.submitMultiOrderForm(payload?.ban, payload?.type, payload?.isbankSelected, payload?.sorryCredit, payload?.sorryDebit, payload?.details, payload?.sub)).pipe(
            map(({ data }: any) => {
              if (data.length > 0) {
                const mutliOrderAllSuccess = data.find((currentElement: any) => currentElement.OrderFormStatus === 'Confirmation');
                if (mutliOrderAllSuccess) {
                  return submitMultiOrderPaymentActionCompleted(data);
                }
              }
              return submitMultiOrderPaymentActionFailed({ error: true });
            }),
            catchError((error: any) => of({ ...submitMultiOrderPaymentActionFailed(error), error: true }))
          )
        )
      );
  }

  private get getRedirectUrl(): IGetRedirectUrlEpic {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(getRedirectUrl.toString()),
        mergeMap((action: any) =>
          from(this.client.getRedirectUrl()).pipe(
            map(({ data }: IGetRedirectUrlEpicResponse) => redirectUrlSuccess(data)),
            catchError((error: any) => of({ ...redirectUrlFailure(error), error: true }))
          )
        )
      );
  }

  private get getInteracBankInfo(): IInteracBankInfoEpic {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(getInteracBankInfo.toString()),
        mergeMap(({ payload }: ReduxActions.Action<{ code: string }>) =>
          from(this.client.getInteracBankInfo(payload?.code)).pipe(
            mergeMap(({ data }: IInteracBankInfoEpicResponse) => {
              return of(
                setIsLoading(false),
                interacBankInfoSuccess(data)
              );
            }),
            catchError((error: any) =>
              of(
                setIsLoading(false),
                { ...interacBankInfoFailure(error), error: true }
              )
            ),
            startWith(setIsLoading(true))
          )
        )
      );
  }

  private get cancelPreauthPaymentsEpic(): ICancelPreauthPayments {
    return (action$: any, store: Store) =>
      action$.pipe(
        ofType(cancelPreauthAction.toString()),
        mergeMap(({ payload }: ReduxActions.Action<{ bans: string[] }>) =>
          from([setIsLoading(true)]).pipe(
            mergeMap(() =>
              from(this.client.cancelPreauth(payload?.bans)).pipe(
                mergeMap(({ data }: ICancelPreauthPaymentsEpicResponse) => {
                  if (data.length > 0) {
                    const successCancelPreauth = data.filter(item => item.success);
                    if (successCancelPreauth && successCancelPreauth.length > 0) {
                      return from([setIsLoading(false), cancelPreauthSuccessAction(data)]);
                    }
                  }
                  return from([setIsLoading(false), cancelPreauthFailureAction({ error: true })]);
                }),
                catchError((error: any) =>
                  from([setIsLoading(false), { ...cancelPreauthFailureAction(error), error: true }])
                )
              )
            )
          )
        )
      );
  }
}

