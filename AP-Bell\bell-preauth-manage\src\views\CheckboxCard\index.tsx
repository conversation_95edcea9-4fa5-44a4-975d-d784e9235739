import React, {useRef, useState} from "react";
import { Button, Checkbox, HeadingStep, Icon, Text } from "@bell/bell-ui-library";
import {CheckboxCardBill} from "./CheckboxCardBill";
import {BillSelected} from "./BillSelected";
import { FormattedMessage, injectIntl } from "react-intl";
import { AlertErrorForm, AlertNotificationList, AlertNotificationListItem } from "../Alert";
import { AccountInputValues, CurrentSection, PaymentItem, PaymentItemAccountType, PaymentItemAccountTypeName, TransactionIdItems } from "../../models";
import { getItemAccountTypeName, getPaymentItemCardType } from "../../utils/PaymentItemUtils";
import { CheckboxCardCurrentBalance } from "./CheckboxCardCurrentBalance";
import { CurrentBalancedSelected } from "./CurrentBalancedSelected";
import { IStoreState } from "../../store/Store";
import { connect } from "react-redux";
import { createMultiPaymentAction, OmnitureOnLoad } from "../../store/Actions";
import { getBanSpecificTransactionId } from "../../utils";
export * from "./CheckboxCardBill";

interface SelectBillsComponentProps {
  intl: any;
  paymentItem: PaymentItem[];
  isShow?: boolean;
  onCurrentSteps: (step:any) => void;
  setCurrentSection: (section: CurrentSection) => void;
  currentSection: CurrentSection;
  setCheckedBillItems: Function;
  paymentItems: PaymentItem[];
  accountInputValues: AccountInputValues[];
  setAccountValues: Function;
  transactionIds: TransactionIdItems[];
  managePreauth?: string;
  isCheckedBan?: string;
}

interface MapDispatchToProps {
  createMultiPaymentData: (ban: string, type: boolean,  details: any ,sub?: string| null) => void;
  createOmnitureOnLoad: Function;
}


const SelectBillsComponent = (
  { intl,
    paymentItem,
    isShow, 
    onCurrentSteps,
    setCurrentSection,
    currentSection,
    setCheckedBillItems,
    paymentItems,
    createMultiPaymentData,
    accountInputValues,
    setAccountValues,
    transactionIds,
    createOmnitureOnLoad,
    managePreauth,
    isCheckedBan,
  }:SelectBillsComponentProps & MapDispatchToProps) => {
  // const [selectedBills, setSelectedBills] = useState<string[]>([]);
  const [showError, setShowError] = useState(false);
  const [checkedBan, setCheckedBan] =  useState<string[]>();
  const [isCheckedItems, setIsCheckedItems] = useState<PaymentItem[]>([]);
  const [ tablePaymentItems, setTablePaymentItems] = useState<PaymentItem[]>([]);
  const [ isSessionPresent, setSessionPresent ] = useState(false);
  const [ isManageBanChecked, setManageBanChecked ] = useState<string[]>();
  const [isSelectAll, setSelectAll] = useState(false);


  // Create a ref array to store refs for each CheckboxCardBill
  const checkboxRefs = useRef<Array<HTMLInputElement | null>>([]);
  const checkAllCheckboxes = () => {
    let status = false;
    if (tablePaymentItems && tablePaymentItems.length > 0) {
      const checkedItems = checkboxRefs.current.filter((checkbox) => checkbox?.checked);
      const CheckedBandetailsString = checkedItems
        .map((checkbox) => checkbox?.getAttribute("data-banDetail"))
        .filter((item): item is string => item !== null && item !== undefined);
      setCheckedBan(CheckedBandetailsString);

      if (checkedItems.length <= 0) {
        status = false;
      } else {
        status = true;
      }
            
    }
    return status;
  };
    
  const SelectAllCheckboxes = (event: React.ChangeEvent<HTMLInputElement>) => {
    // let status = false
    const checkedStatus = event.target.checked;
    if(checkedStatus){
      setSelectAll(true);
      if (tablePaymentItems && tablePaymentItems.length > 0) {   
        setIsCheckedItems(tablePaymentItems);            
        checkboxRefs.current.forEach((checkbox) => {
          if (checkbox) 
          {
            checkbox.checked = checkedStatus;                 
                    
          }
                
        });            
            
      }        
    }
    else
    {
      setSelectAll(false);
      tablePaymentItems.map((item, index) =>
      {
        setIsCheckedItems((isCheckedItems: any) => isCheckedItems.filter((checkedItem: any) => checkedItem.BillName !== item.BillName));
      });
        
      if (tablePaymentItems && tablePaymentItems.length > 0) {
        checkboxRefs.current.forEach((checkbox) => {
          if (checkbox) checkbox.checked = false;
        });            
            
      }
    }       
  };
  const OnClickNext = () => {
    if (isCheckedItems !== null && isCheckedItems.length > 0) {
            
      createMultiPaymentData(isCheckedItems[0].Ban, 
        isCheckedItems[0].AccountType === PaymentItemAccountType.OneBill, 
        accountInputValues,
        isCheckedItems[0].subscriberId);
    }
    if(checkAllCheckboxes() === false){
      setShowError(true); 
    }else{
      setShowError(false);
      setCurrentSection(CurrentSection.PaymentMethod);
            
    }
  };
    

  // Callback to handle Edit icon
  const handleIconLinkClick = (steps:any) => {
    setCurrentSection(CurrentSection.SelectBills);
    createOmnitureOnLoad("BAN_SELECT");
  };

  const isManage = managePreauth && managePreauth !== null;

  const checkedManageBan = isCheckedBan && isManage ? new Array(isCheckedBan) : [];

  React.useEffect(() => {
    const items = sessionStorage.getItem("itemsChecked");
    const storedArray = items && JSON.parse(items);
    if (storedArray !== null && storedArray.length > 0) {
      setSessionPresent(true);
      setManageBanChecked(storedArray);

      setTablePaymentItems(paymentItems);
      // make API call with the selected BANs
      const accountValuesArr = getAccountInputValues(paymentItems);
      createMultiPaymentData(paymentItems[0].Ban, 
        paymentItems[0].AccountType === PaymentItemAccountType.OneBill, 
        accountValuesArr,
        paymentItems[0].subscriberId);
    } else {

      setManageBanChecked(checkedManageBan);
      setTablePaymentItems(paymentItems);
    }
  }, []);

  React.useEffect(() => {
    if (isSessionPresent && tablePaymentItems?.length > 0)
    {
      OnClickNext();
      sessionStorage.removeItem("itemsChecked");
      setSessionPresent(false);
    }
  }, [isSessionPresent]);

  
  const getCheckboxCCText = (paymentItem: PaymentItem) => {
    if((paymentItem.IsOnPreauthorizedPayments || (managePreauth && managePreauth === "Creditcard" && paymentItem.CreditCardDetails)) && paymentItem.CreditCardDetails){
      return (
        <>
          <FormattedMessage 
            id="SELECT_BILLS_CC_DESC" 
            values={{ 
              CreditCardType: getPaymentItemCardType(paymentItem.CreditCardDetails.CreditCardType),
              CCFourDigits: paymentItem.CreditCardDetails.CreditCardNumber.slice(-4),
              ExpiryDate: paymentItem.CreditCardDetails.ExpireMonth + "/" + paymentItem.CreditCardDetails.ExpireYear 
            }}
          />
        </>
      );
    }
    else if((paymentItem.IsOnPreauthorizedPayments || (managePreauth && managePreauth === "Debit" && paymentItem.BankAccountDetails)) && paymentItem.BankAccountDetails) {
      return (
        <>
          <FormattedMessage 
            id="SELECT_BILLS_BANK_DESC" 
            values={{ 
              BankName: paymentItem.BankAccountDetails.BankName,
              Code: paymentItem.BankAccountDetails.TransitCode,
              BankMaskedDigits: paymentItem.BankAccountDetails.AccountNumberMaskedDisplayView,                   
            }}
          />
        </>
      );
      // return bankDetails Here 
        
    } else {
      return <>{intl.formatMessage({id: "ACCOUNT_BALANCE"})}</>;
    }
  };

  // For disabling next button for all accounts on registered pre auth NOTe: need to remove once manage flow was develop
  const disabledButtonIsPreauth:boolean =  paymentItem?.filter((x) => x.IsOnPreauthorizedPayments).length === paymentItem.length;
    
  // set to monitor current steps for dynamic tile (END)
  const getAccountInputValues = (accountInputArr: PaymentItem[]) => {
    const transformedData: AccountInputValues[] = accountInputArr.map(item => ({
      accountNumber: item.Ban,
      subNumber: item.subscriberId,
      transactionID: getBanSpecificTransactionId(item.Ban, transactionIds),
      payBalanceAmnt: 0
    }));
    return transformedData;
      
  };

  React.useEffect(() => {
    if (paymentItem.length > 1) {
      setCheckedBillItems(isCheckedItems);
      const transformedData = getAccountInputValues(isCheckedItems);
      setAccountValues(transformedData);
      if (isCheckedItems.length === 0)
      {
        setCheckedBillItems([]);
        setAccountValues([]);
      } 
      if(paymentItem.length === isCheckedItems.length)
        setSelectAll(true);
      else
        setSelectAll(false);
    }  
  }, [isCheckedItems]);
    
  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };

  const isShowLabel = (item: PaymentItem): boolean | undefined => {
    if(!item.IsNM1Account && item.AccountType === PaymentItemAccountType.OneBill && !item.IsOnPreauthorizedPayments) {
      return false;
    } else if ((item.Due === 0 || item.Due === undefined) && !item.IsOnPreauthorizedPayments) {
      return false;
    } else {
      return true;
    }        
  };
  React.useEffect(() => {
    const items = sessionStorage.getItem("itemsChecked");
    const storedArray = items && JSON.parse(items);
    if (currentSection === CurrentSection.SelectBills) {
      if (storedArray !== null && storedArray.length > 0) { }
      else {
        if (!showError) {
          createOmnitureOnLoad("BAN_SELECT");
        }
        else {
          createOmnitureOnLoad("BAN_SELECT_ERROR");
        }
      }
    }
  }, [currentSection, showError]);


  return (
    <div className={["payment-border-b payment-border-gray-4", isShow ? "" : "payment-hidden"].join(" ").trim()}>
             
      <div className={["payment-flex payment-flex-col", currentSection === CurrentSection.SelectBills ? "" : "payment-hidden"].join(" ").trim()}>
        <HeadingStep 
          autoScrollActiveStep={false}
          disableSrOnlyText={true}
          status="active"
          subtitle=""
          hideSubtitle
          variant="leftAlignNoStep"
          title={paymentItem.filter((x) => x?.IsOnPreauthorizedPayments).length > 1 ? 
            intl.formatMessage({id: "SELECT_BILLS_HEADING"}) : 
            intl.formatMessage({id: "SELECT_BILLS_HEADING_SINGULAR"})
          }
          id={currentSection === CurrentSection.SelectBills ? "payment-manage-heading" : undefined} 
          aria-hidden={currentSection === CurrentSection.SelectBills ? true : undefined} 
          // SELECT_BILLS_HEADING_SINGULAR french needs to update once
        />
        <p className="payment-text-gray payment-text-14 payment-mt-5">                
          {isManage ? intl.formatMessage({id: "SELECT_BILLS_HEADING_DESC_MANAGE"}) : intl.formatMessage({id: "SELECT_BILLS_HEADING_DESC"})}
        </p>
      </div>
      {showError && (
        <>
          <div className="payment-mt-30"></div>
          <AlertErrorForm>
            <AlertNotificationList>
              <AlertNotificationListItem 
                label={intl.formatMessage({id: "ALERT_ERROR_SELECT_BILL_INFO"})}
                labelDescription={intl.formatMessage({id: "ALERT_ERROR_SELECT_BILL_DESC"})}
                variant="errorList"
                id="error-alert-2"
              />
            </AlertNotificationList>
          </AlertErrorForm>
        </>
      )}
      <div className={["payment-pt-0 checkbox-main", currentSection === CurrentSection.SelectBills  ? "" : "payment-hidden" ].join(" ").trim()}>
        <div className="payment-mt-30">
          {/* Select all Checkbox */}
          <Checkbox 
            id="chxbx1" 
            name="checkboxname" 
            value="select all" 
            variant="default"
            checked={isSelectAll}   
            onChange={SelectAllCheckboxes}>
            <label>
              {intl.formatMessage({id: "SELECT_ALL_BAN"})}
            </label>
          </Checkbox> 
        </div>   
        <div className="payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap chkbx-manage-main" role="group" aria-labelledby="chekcboxgroup-label">
          {tablePaymentItems && tablePaymentItems.length > 0 && tablePaymentItems.sort((a, b) => a.NickName > b.NickName ? 1 : -1).map((item, index) => (
            <CheckboxCardBill 
              className={["checkboxitem payment-group/checkboxcard  payment-basis-0 sm:payment-basis-[47%] md:payment-basis-[35%] lg:payment-basis-1/3 payment-w-full sm:payment-w-auto payment-mr-15 payment-mb-15", (item.IsOnPreauthorizedPayments && !isManage) ? "payment-bg-gray-3" : "" ,].join(" ").trim()}
              id={`checkboxbill-${index}`}
              idIndex={index}
              label={`checkboxBill${index}-label-${index} checkboxBillBalance-${index}-label-${index}`}
              isChecked={isManage && isManageBanChecked && isManageBanChecked.length > 0 ? Boolean(isManageBanChecked?.find(element => element === item.Ban)) : item.IsChecked}
              // isChecked={} //todo: check on acceptance criteria on check state
              isDisabled={item.IsOnPreauthorizedPayments && !isManage}
              billType={getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename)}
              billAccountNumber={item.NickName ?? item.BillName}
              // text={} //todo: check what model object to use for this props
              text={getCheckboxCCText(item)}
              priceSettings={!item.IsOnPreauthorizedPayments? {price: item.Due} : undefined}
              ref={(el) => {(checkboxRefs.current[index] = el);}}
              item={item}
              isCheckedItems={isCheckedItems}
              setIsCheckedItems={setIsCheckedItems}
              isShowLabel={isShowLabel(item)}
              paymentItems={tablePaymentItems}
              intl={intl}
            />
          ))}

        </div>
        {showError && (
          <Text elementType="div" className="payment-pb-15 payment-flex payment-items-center" id="error-alert-3">
            <Icon className="payment-text-15 payment-text-red payment-mr-10" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
            <Text elementType="div" className="payment-text-red payment-text-12">{intl.formatMessage({id: "ALERT_ERROR_ONE_SELECT_BILL"})}</Text>
          </Text>
        )}          
        <div className="payment-pt-15 payment-pb-45 sm:payment-pb-60">
          <Button variant="primary" onClick={OnClickNext}  disabled={isManage ? false :  disabledButtonIsPreauth}>{intl.formatMessage({id: "CTA_NEXT"})}</Button>
        </div>
      </div>
            
      <BillSelected 
        isActive={currentSection > CurrentSection.SelectBills} 
        onIconLinkClick={handleIconLinkClick} 
        banDetails={checkedBan || []}
        isCheckedItems={isCheckedItems}
        isShow={isShow}
      />
    </div>
  );
};

const mapStateToProps = (state: IStoreState): MapStateToProps => (
  {
    createPayment: state.createPayment
  }
);
interface MapStateToProps {
  createPayment: any;
}

const mapDispatchToProps = (dispatch: any): MapDispatchToProps => ({
        
  createMultiPaymentData: (ban: string, type: boolean , details: any, sub?: string | null) => dispatch(createMultiPaymentAction({ ban, type, details,sub })),

  createOmnitureOnLoad : (data: string) => dispatch(OmnitureOnLoad({data})) ,
});


export const SelectBills  = connect(mapStateToProps, mapDispatchToProps)(injectIntl(SelectBillsComponent));

interface CurrentBalanceComponentProps {
  intl: any;
  paymentItem: PaymentItem[];
  checkedBillItems: PaymentItem[];
  checkedCurrentBalanceItems: PaymentItem[];
  setCheckedCurrentBalanceItems: Function;
  isShow?: boolean;
  onCurrentSteps: (step: any) => void;
  setCurrentSection: (section: CurrentSection) => void;
  currentSection: CurrentSection;
  language: "en" | "fr";
  accountInputValues: AccountInputValues[];
  setAccountValues: Function;
  transactionIds: TransactionIdItems[];
  isBankPaymentSelected: boolean;
  setNotOptedBalanceItems: Function;
  createOmnitureOnCurrentBalance: any;
}


const CurrentBalanceComponent = ({ 
  intl, 
  paymentItem,
  checkedBillItems,
  setCheckedCurrentBalanceItems,
  setCurrentSection,
  currentSection,
  language,
  accountInputValues,
  setAccountValues,
  transactionIds,
  isBankPaymentSelected,
  setNotOptedBalanceItems,
  checkedCurrentBalanceItems,
  createOmnitureOnCurrentBalance
}:CurrentBalanceComponentProps) => {
  const [isCheckedBalanceItems, setIsCheckedBalanceItems] = useState<PaymentItem[]>([]);

  const OnClickNext = () => {
    setCurrentSection(CurrentSection.TermsAndCondition);
  };

  //  Callback to handle Edit icon
  const handleIconLinkClick = (steps:any) => {
    setCurrentSection(CurrentSection.CurrentBalance);
  };

  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };
    
  React.useEffect(() => {
    
    const updatedAccountInputValues: AccountInputValues[] = [...accountInputValues]; // Create a copy of the array

    if (isCheckedBalanceItems.length > 0)
    {
      setCheckedCurrentBalanceItems(isCheckedBalanceItems);

      const transformedData: AccountInputValues[] = updatedAccountInputValues.map(existing => {
        // Find a matching item from isCheckedBalanceItems
        const matchingItem = isCheckedBalanceItems.find(item =>
          item.Ban === existing.accountNumber && 
                  item.subscriberId === existing.subNumber
        );
              
        // If a matching item is found, update the payBalanceAmnt
        if (matchingItem) {
          existing.payBalanceAmnt = matchingItem.Due;
        }else{
          existing.payBalanceAmnt = 0;
        }
              
        // Return the modified (or unmodified) object
        return existing;
      });

      setAccountValues(transformedData);
      setNotOptedBalanceItems(checkedBillItems.filter((item) => item.Due > 0 && item.AccountType !== PaymentItemAccountType.OneBill && !isCheckedBalanceItems.includes(item)));

    }  else {
      setCheckedCurrentBalanceItems([]);
      setAccountValues(updatedAccountInputValues);
      setNotOptedBalanceItems(checkedBillItems.filter((item) => item.Due > 0 && item.AccountType !== PaymentItemAccountType.OneBill));
    }
  }, [isCheckedBalanceItems]);
    

    
  const PAY_CURRENT_BALANCE_DESC = intl.formatMessage({id: "PAY_CURRENT_BALANCE_DESC"});

  return (
          
    <>
      <div className={[currentSection === CurrentSection.CurrentBalance? "payment-border-b payment-border-gray-4 payment-border-t payment-mt-45" : "payment-border-b payment-border-gray-4", currentSection > CurrentSection.CurrentBalance ? "payment-hidden" :  "" ].join(" ").trim()}>
                
        <div>
          <HeadingStep 
            autoScrollActiveStep={false}
            disableSrOnlyText={true}
            status={currentSection === CurrentSection.CurrentBalance? "active" : "inactive"}
            subtitle=""
            hideSubtitle
            variant="leftAlignNoStep"
            title={checkedBillItems.filter((x) => !x?.IsOnPreauthorizedPayments).length > 1 ? 
              intl.formatMessage({id: "PAY_CURRENT_BALANCE_HEADING"}) : 
              intl.formatMessage({id: "PAY_CURRENT_BALANCE_HEADING"})
            }
            id={currentSection === CurrentSection.CurrentBalance ? "payment-manage-heading" : undefined}
            aria-hidden={currentSection === CurrentSection.CurrentBalance ? "true" : undefined}

          />
          {currentSection === CurrentSection.CurrentBalance &&
                        <div className="payment-flex sm:payment-items-center">
                          <Icon
                            className="brui-text-blue payment-mt-10"
                            iconClass="bi_brui"
                            iconName="bi_info_notif_small"
                          /> 
                          <p 
                            className="payment-text-gray payment-text-14 payment-leading-18 payment-mt-10 payment-ml-10"
                            dangerouslySetInnerHTML={{__html: PAY_CURRENT_BALANCE_DESC}}
                          />
                        </div>
          }
        </div>
        <div className={[ currentSection === CurrentSection.CurrentBalance ? "" : "payment-hidden" ].join(" ").trim()}>
          <div className="payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap" role="group" aria-labelledby="chekcboxgroup-label">
            {checkedBillItems.map((item, index) => (
              item.Due > 0 && item.AccountType !== PaymentItemAccountType.OneBill ? (
                <CheckboxCardCurrentBalance
                  className="sm:payment-px-30 payment-mb-15"
                  id={`checkboxBill-${index}`}
                  idIndex={index}
                  label={`checkboxBalance-${index}-label-${index} checkboxBalance-${index}-label-${index}-info`}
                  isDisabled={item.IsOnPreauthorizedPayments}
                  billType={getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename)}
                  billAccountNumber={item.NickName ?? item.BillName}
                  text={intl.formatMessage({id: "PAY_MY_BALANCE"})}
                  priceSettings={ !item.IsOnPreauthorizedPayments ? {price: item.Due} : undefined}
                  currentItem={item}
                  isCheckedBalanceItems={isCheckedBalanceItems}
                  setIsCheckedBalanceItems={setIsCheckedBalanceItems}
                                
                />
              ) : null
            ))}
          </div> 
          <div className="payment-text-gray payment-text-12 payment-mt-20">
            <p>{intl.formatMessage({id: "PAY_CURRENT_BALANCE_NOTE_1"})}</p>     
            <p>{intl.formatMessage({id: "PAY_CURRENT_BALANCE_NOTE_2"})}</p>     
          </div>
          <div className="payment-pt-15 payment-pb-45 sm:payment-pb-60">
            <Button variant="primary" 
              onClick={OnClickNext} >{intl.formatMessage({id: "CTA_NEXT"})}
            </Button>
          </div>
        </div>


                
      </div>

      <CurrentBalancedSelected
        isActive={currentSection > CurrentSection.CurrentBalance} 
        onIconLinkClick={handleIconLinkClick} 
        paymentItem={paymentItem}
        isCheckedBalanceItems={isCheckedBalanceItems}
        checkedBillItems={checkedBillItems}
        isBankPaymentSelected={isBankPaymentSelected}
        currentSection={currentSection}
        language={language}/> 
    </>
  
       
  );
};



export const CurrentBalance  = injectIntl(CurrentBalanceComponent);

