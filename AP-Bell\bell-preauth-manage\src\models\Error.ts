import { ApplicationError } from "bwtk";

export class WidgetError extends ApplicationError {
  constructor(message: string = "", public errorType: ErrorTypes = ErrorTypes.None) {
    super(message);
  }
}
export class APIError extends ApplicationError {
  constructor(public statusCode: number, public message: string, public serverMessage: string, public apiName: string, public errorType: ErrorTypes = ErrorTypes.WebAPIError) {
    super(message);
  }
}

export enum ErrorTypes {
  None,
  NoDataError,
  WebAPIError,
  GetPreviousBillAPIError,
  Exception,
  ApplicationError,
  NoTooltipDataError,
  GetOverageAPIError,
}

export enum InputErrorTypes {
  Empty = "EMPTY",
  CreditCardExpireDate = "CREDIT_CARD_EXPIRY",
  Invalid = "INVALID"
}

export interface IValidationErrors {
  field: string,
  valErrors: Array<InputErrorTypes>;
}

export class ValidationErrors {
  errors: Array<IValidationErrors>;
}
