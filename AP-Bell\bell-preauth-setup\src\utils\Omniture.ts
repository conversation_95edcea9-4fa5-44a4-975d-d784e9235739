// s_oPYM:bank, Credit
// s_oCCDT:credit card type
import { IBankInfoRes, InputBankAccountDetail, PaymentItem } from "../models"; // Ensure this path is correct or update it to the correct path

export const getCardType = (cardtype: string) => {
  if(cardtype === "MASTERCARD") return "MC";
  if(cardtype === "AMEX") return "AX";
  if(cardtype === "VISA") return "VI";
  return cardtype;
}

export const IsInteracValueChanged = (bankInfo: IBankInfoRes, bankAccountDetail?: InputBankAccountDetail) => {
  let result = false;
  if(bankInfo.bankAccountNumber != "" && bankAccountDetail && bankAccountDetail.AccountNumber != "")
   {
    if(bankAccountDetail.AccountHolder !== bankInfo.accountHolderName || bankAccountDetail.BankName !== bankInfo.bankCode || bankAccountDetail.TransitNumber !== bankInfo.transitNumber || bankAccountDetail.AccountNumber !== bankInfo.bankAccountNumber )
    {
      result = true;
    }
  }
  return result;
}

export const getConfirmationOmniture = (
  isBankpaymentSelscted: boolean,
  showCurrentBalance: boolean,
  checkedCurrentBalanceItems: PaymentItem[],
  submitMultiOrderPayment: any,
  InteracCode:string,
  CreditCardType?: string,
) => {
  
  if (isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Bank payment:optin",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
          s_oILI: InteracCode
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Bank payment:optout",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
          s_oILI: InteracCode
        };
      }
    } else {
      return {
        s_oPYM: "Bank payment",
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        s_oILI: InteracCode
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Credit card:optin",
          s_oCCDT: {CreditCardType},
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
          s_oILI: InteracCode
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Credit card:optout",
          s_oCCDT: {CreditCardType},
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
          s_oILI: InteracCode
        };
      }
    } else {
      return {
        s_oPYM: "Credit card",
        s_oCCDT: {CreditCardType},
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        s_oILI: InteracCode
      };
    }
  }

  return {
    s_oPYM: "",
    s_oCCDT: "",
  };
};


export const getOmnitureOnConfirmationFailure = (
  isBankpaymentSelscted: boolean,
  showCurrentBalance: boolean,
  checkedCurrentBalanceItems: PaymentItem[],
  CreditCardType?: string,
) => {

  if (isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Bank payment:optin",
          s_OPID: ""
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Bank payment:optout",
          s_OPID: ""
        };
      }
    } else {
      return {
        s_oPYM: "Bank payment",
        s_OPID: ""
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Credit card:optin",
          s_oCCDT: {CreditCardType},
          s_OPID: ""
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Credit card:optout",
          s_oCCDT: {CreditCardType},
          s_OPID: ""
        };
      }
    } else {
      return {
        s_oPYM: "Credit card",
        s_oCCDT: {CreditCardType},
        s_OPID: ""
      };
    }
  }

  return {
    s_oPYM: "",
    s_oCCDT: "",
    s_OPID: ""
  };
}

export const IsDetailsValid = (validateMultiOrderPayment: any) => {
  let result = false ;
  if (Object.values(validateMultiOrderPayment).length > 0) {
    for (let item of Object.values(validateMultiOrderPayment) as any) {
      if (item?.errorCodeID && item?.errorCodeID !== "") {
        result = true;
        break;
      }
    }
  }
  return result;
}