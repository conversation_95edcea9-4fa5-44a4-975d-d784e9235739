import React, { ComponentPropsWithRef, forwardRef} from "react";
import {RadioCard, Heading, Text} from "@bell/bell-ui-library";

// import Interac_box_logo from "../assets/images/interac-logos/Interac_box_logo.svg";


export interface RadioCardBankDetailsProps extends ComponentPropsWithRef<"input"> {
  id: string;
  label: string;
  name: string;
  describe?: string;
  defaultChecked?: boolean;
  isInterac?: boolean;
  headingLevel?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  children: React.ReactNode;
  interactIconPath?: string
  // intl: any;

}

export const RadioCardBankDetails = forwardRef<HTMLInputElement, RadioCardBankDetailsProps>(function RadioCardBankDetails(
  {
    id,
    label,
    name,
    describe,
    defaultChecked = false,
    isInterac,
    headingLevel = "h4",
    children,
    // intl,
    onChange,
    interactIconPath
  },
  ref
){


  return (
    <>
      <RadioCard
        className="payment-manage-radio-bank brui-border brui-border-gray-3 payment-py-30 sm:!brui-px-30 payment-group/radiocard"
        id={id}
        name={name}
        defaultChecked={defaultChecked}
        aria-labelledby={"label-" + id}
        aria-describedby={describe ? ('desc-' + id) : ''}
        radioPlacement="topLeft"
        ref={ref}
        onChange={onChange}
      >
        <div className="payment-pl-[34px] sm:payment-pr-[30px] brui-relative brui-flex brui-justify-between">
          <Heading
            level={headingLevel}
            variant="default"
            className="brui-font-sans group-has-[input[type=radio]:checked]/radiocard:payment-font-bold brui-mb-10 sm:brui-mb-5 brui-text-16 brui-leading-20 payment-mt-5"
            id={"label-" + id}
          >
            {label}
          </Heading>
          {isInterac && (
          // <img alt="" className="brui-ml-5 sm:brui-absolute brui-right-0 brui-top-0 sm:brui-w-40 sm:brui-h-40 brui-w-32 brui-h-32" src={interactIconPath} />
            <img alt="" className="payment-ml-5 payment-relative sm:payment-absolute payment-right-0 payment-top-0 sm:payment-w-40 sm:payment-h-40 payment-w-32 payment-h-32" src={interactIconPath} />
          )}
        </div>
        <div className="sm:payment-pl-[34px] sm:payment-pr-[45px]">
          {describe && (
            <Text
              id={'desc-' + id}
              elementType="p"
              className="brui-text-14 brui-leading-18 brui-text-gray"
            >
              {describe}
            </Text>
          )}
          <div className="brui-z-10 brui-relative">
            <div className="payment-hidden group-has-[input[type=radio]:checked]/radiocard:payment-block">
              {children}
            </div>
          </div>
        </div>
      </RadioCard>
    </>
  );
});

// export default (injectIntl(RadioCardBankDetails))
export default RadioCardBankDetails;
