import React from "react";
import { But<PERSON> } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import { AlertErrorFailureCancel, AlertNotificationList, AlertNotificationListItem, AlertConfirmationSuccessCancel } from "../Alert";
import { PaymentItemAccountTypeName, PaymentItem } from "../../models";
import { getItemAccountTypeName } from "../../utils";


interface CancellationPartiallyFailedComponentProps {
  someBansFailed: PaymentItem[];
  language: "en" | "fr";
  intl: any;
}


const CancellationPartiallyFailedComponent = (props: CancellationPartiallyFailedComponentProps) => {
  const { intl, someBansFailed, language } = props;

  const accountTypename: PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({ id: "ACCOUNT_TYPENAME_MYBILL" }),
    Mobility: intl.formatMessage({ id: "ACCOUNT_TYPENAME_MOBILITY" }),
    OneBill: intl.formatMessage({ id: "ACCOUNT_TYPENAME_ONEBILL" }),
    TV: intl.formatMessage({ id: "ACCOUNT_TYPENAME_TV" }),
    Internet: intl.formatMessage({ id: "ACCOUNT_TYPENAME_INTERNET" }),
    HomePhone: intl.formatMessage({ id: "ACCOUNT_TYPENAME_HOMEPHONE" }),
    MobilityAndOneBill: intl.formatMessage({ id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL" }),
    SingleBan: intl.formatMessage({ id: "ACCOUNT_TYPENAME_SINGLEBAN" })
  };

  const someFailedBansItems = [{ 
    description: "",
    items: someBansFailed.map(item => ({
      label: intl.formatMessage(
        { id: "CANCEL_FAILED_BANS_ACCOUNT_TITLE" },
        { accounttype: getItemAccountTypeName(item.AccountType, item.IsNM1Account, accountTypename) }
      ),
      accountNumber: item.NickName,
    }))
  }];

  const handleOnClick = () => {
    window.location.href = '/';
  };

  const multiBanFail = someBansFailed.length > 1;

  return (
    <div>
      <div className="payment-mt-30 sm:payment-mt-[60px] payment-pb-15 sm:payment-pb-15">
        <AlertErrorFailureCancel multiBanFail={multiBanFail}>
          {someFailedBansItems && someFailedBansItems.map((item) => (
            <AlertNotificationList
              label={item.description}>
              {item.items.map((item2) => (
                <AlertNotificationListItem
                  label={item2.label}
                  labelDescription={item2.accountNumber}
                  variant="accountListSimple"
                  priceSettings={{ language, showZeroDecimalPart: true }}>
                </AlertNotificationListItem>
              ))}
            </AlertNotificationList>
          ))}
        </AlertErrorFailureCancel>
      </div>
            
      {/* Success Alert */}
      <div className="">
        <AlertConfirmationSuccessCancel></AlertConfirmationSuccessCancel>
      </div>

      {/* Back to mybell button */}
      <div className="payment-mt-15 sm:payment-mt-30 payment-mb-45 sm:payment-mb-60">
        <Button onClick={handleOnClick}
          size="regular"
          variant="secondary">
          {intl.formatMessage({id: "BACK_TO_MY_BELL"})}
        </Button>
      </div>
    </div>
  );
};

export const CancellationPartiallyFailed = injectIntl(CancellationPartiallyFailedComponent);
