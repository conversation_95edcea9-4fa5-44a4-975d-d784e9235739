import React, { forwardRef } from "react";
import { FormControl, Text, Icon, InputText, Label, FormGroup, Select, SelectOption, ListItem, Price } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import { LightBoxNoname, LightBoxSecurityCode } from "../LightBox";
import { getMonthList, getYearList, numberOnly } from "../../utils";
import { PaymentRadioCard } from "./PaymentMethodRadio";
import { CreditCardPaymentProps } from "../../models/CreditCardPaymentPropsModel";
import { NotifCard } from "../NotifCard";

const CreditCardPaymentComponent = forwardRef<HTMLInputElement, CreditCardPaymentProps>(function CreditCardPaymentComponent(
  {
    intl,
    Checked,
    onChange,
    isPreauth,
    hasCreditCardDetails,
    bankitems,
    radioRef,
    handleBankRadioChange,
    isBankChecked,
    cardNumber,
    handleCreditCardChange,
    inputRefs,
    cardIcons,
    cardType,
    errorCardNumber,
    errorCardName,
    errorExpiryDate,
    errorSecurityCode,
    handleMaskCVV,
    CVV,
    creditCardAutopayOffers,
    debitCardAutopayOffers,
    checkedBillItems,
    language,
    IsAutopayCreditEnabled
  },
  ref
) {
  const CREDITCARD_PAYMENT_LABEL = intl.formatMessage({ id: "CREDIT_CARD_LABEL" });

  const [selectedyear, setselectedyear] = React.useState(intl.formatMessage({ id: "CREDIT_CARD_YEAR_TEXT" }));
  const [selectedMonth, setselectedmonth] = React.useState(intl.formatMessage({ id: "CREDIT_CARD_MONTH_TEXT" }));
  const Setselectedyear = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setselectedyear(event.target.value);
  };
  const Setselectedmonth = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setselectedmonth(event.target.value);
  };
  const Expiration = intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_DATE_SR_LABEL" }, {
    month: selectedMonth,
    year: selectedyear
  });


  const checkedBanOffers = () => {
    const filteredOffer: any = [];
    creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems?.map((billItem) => {
        if (item.Ban === billItem.Ban) {
          filteredOffer.push(item);
        }
      });
    });
    return filteredOffer;
  };
  const checkedDebitBanOffers = () => {
    const filteredOffer: any = [];
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems?.map((billItem) => {
        if (item.Ban === billItem.Ban) {
          filteredOffer.push(item);
        }
      });
    });
    return filteredOffer;
  };
  const CreditOffers =
  {
    label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
    credits: bankitems && bankitems.length > 1 ? checkedBanOffers() : creditCardAutopayOffers
  };
  const getTotalOffers = () => bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {

    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0) : creditCardAutopayOffers && creditCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {

    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  const isTrueAutopay = () => CreditOffers?.credits?.some((debit: any) =>
    debit.AutopayEligibleSubscribers?.some((item: any) =>
      item?.autopayOffers?.some(
        (credit: any) =>
          credit.eligiblePaymentMethods?.includes("C") &&
          credit.eligiblePaymentMethods?.includes("D")
      )
    )
  ) || false;
  const getTotalDebitOffers = () => bankitems && bankitems.length > 1 ? checkedDebitBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {

    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0) : debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {

    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  return (
    <div className="brui-mb-15">
      <PaymentRadioCard
        id="payment-radio-credit"
        name="payment-radio"
        label={CREDITCARD_PAYMENT_LABEL}
        headingLevel="h3"
        defaultChecked={Checked ? true : undefined}
        ref={ref}
        onChange={onChange}
      >
        {IsAutopayCreditEnabled && <div>

          <NotifCard
            hasNotifCard={getTotalOffers() > 0}
            variant="greatNews"
            label={intl.formatMessage({ id: "GREAT_NEWS" })}
            label1={
              isTrueAutopay()
                ? getTotalOffers() > 1
                  ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY" })
                  : intl.formatMessage({ id: "LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY" })
                : getTotalOffers() > 1
                  ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_CREDIT_TITLE" })
                  : intl.formatMessage({ id: "LABEL_LOADED_OFFER_CREDIT_TITLE" })
            }
            label2={intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING" })}
            label3={
              getTotalOffers() > 1
                ? intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE" })
                : intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })
            }
          >
            {CreditOffers?.credits?.map((debit: any, debitIdx: number) => (
              <React.Fragment key={debit.Ban || debitIdx}>
                {bankitems && bankitems.length > 1 && (
                  <p className="payment-text-14 payment-text-gray">
                    {debit.banInfo && debit.banInfo.nickName}:
                  </p>
                )}
                {debit.AutopayEligibleSubscribers?.map((item: any, itemIdx: number) =>
                  item?.autopayOffers?.map((credit: any, creditIdx: number) => (
                    <ul
                      className="payment-list-disc payment-list-inside payment-mb-10"
                      key={`${item.subscriberTelephoneNumber}-${creditIdx}`}
                    >
                      <ListItem className="payment-text-14 payment-text-gray payment-leading-18">
                        {item.subscriberTelephoneNumber} -&nbsp;
                        <Price
                          className="brui-text-18 brui-text-blue brui-text-darkblue payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal"
                          showZeroDecimalPart={true}
                          price={credit.discountAmount}
                          variant="defaultPrice"
                          suffixText="perMonth"
                          language={language}
                        />
                      </ListItem>
                    </ul>
                  ))
                )}
              </React.Fragment>
            ))}
          </NotifCard>
          {/* {getTotalOffers() == 0 ?
                    <Text className="brui-text-14 brui-text-gray">
                    {intl.formatMessage({ id: "SORRY_MESSAGE" })} 
                  </Text> : " "
                    } */}
          {creditCardAutopayOffers && creditCardAutopayOffers.length > 0 && getTotalOffers() === 0 && getTotalDebitOffers() > 0 ?
            <Text role="alert" className="payment-bg-gray-3 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10" elementType="div">
              <div className="payment-flex payment-size-20 payment-items-center payment-justify-center">
                <Icon className="payment-text-20 payment-text-blue" iconClass="bi_brui" iconName="bi_info_notif_small"></Icon>
              </div>
              <Text className="payment-text-14 payment-leading-20 payment-text-gray" elementType="p"> {intl.formatMessage({ id: "SORRY_MESSAGE_CREDIT" })}</Text>
            </Text> : ""}
        </div>
        }
        <div>
          <form noValidate>
            {(isBankChecked || (!isPreauth && !isBankChecked) || (isPreauth)) && (
              <>
                {/* card number */}
                <FormControl className="sm:brui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-self-start sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <div>
                      <Label htmlFor="card-number" isError={errorCardNumber} required={true} className={errorCardNumber ? "payment-error-required" : ""}>
                        <span id="cc-number-label" className="brui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_NUMBER_SR_LABEL" })}</span>
                        <span aria-hidden={true}>{intl.formatMessage({ id: "CREDIT_CARD_NUMBER_LABEL" })}</span>
                      </Label>
                    </div>
                    <Text aria-hidden={inputRefs.inputCreditCardNumber?.current?.value ? true : false} elementType="div" className="brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray payment-mt-5">
                      {intl.formatMessage({ id: "CREDIT_CARD_NUMBER_DESC_INPUT_LABEL" })}
                    </Text>
                  </div>

                  <div className="brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap">
                    <InputText
                      value={cardNumber}
                      onChange={handleCreditCardChange}
                      onInput={(e: any) => numberOnly(e)}
                      className="sm:!payment-w-[280px] payment-mr-30"
                      id="card-number"
                      required={true}
                      isError={errorCardNumber}
                      errorMessage={intl.formatMessage({ id: "ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL" })}
                      ref={inputRefs.inputCreditCardNumber}
                      aria-labelledby="cc-number-label"
                    />

                    <div className="sm:payment-h-44 payment-ml-0 payment-mt-10 sm:payment-mt-[6px] brui-flex payment-items-baseline brui-gap-15"
                      aria-label={intl.formatMessage({ id: "CC_IMAGE_SR_LABEL" })}
                      role="img"
                    >
                      {Object.entries(cardIcons).map(([type, iconSrc]) => (
                        <img
                          key={type}
                          src={iconSrc}
                          alt={`${type} card`}
                          className="brui-h-32 payment-mr-15 brui-object-contain"
                          aria-hidden="true"
                          style={{
                            opacity: cardType === type ? 1 : 0.5, // Highlight detected card type
                            transition: "opacity 0.3s ease-in-out",
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </FormControl>

                {/* card name */}
                <FormControl className="sm:brui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex sm:payment-self-start brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <div>
                      <Label htmlFor="text-2" isError={errorCardName} required={true} className={errorCardName ? "payment-error-required" : ""}>
                        <span id="cc-name-label" className="brui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_NAME_SR_LABEL" })}</span>
                        <span aria-hidden={true}>{intl.formatMessage({ id: "CREDIT_CARD_NAME_LABEL" })}</span>
                      </Label>
                    </div>
                    <Text elementType="div" className="brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray">
                      {intl.formatMessage({ id: "CREDIT_CARD_NAME_DESC_INPUT_LABEL" })}
                    </Text>
                  </div>
                  <div className="brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap">
                    <InputText
                      // onBlur={(e) => onCardHolderNameChange(e.target.value)}
                      className="sm:!payment-w-[280px]"
                      id="text-2"
                      aria-labelledby="cc-name-label"
                      required={true}
                      isError={errorCardName}
                      errorMessage={intl.formatMessage({ id: "ERROR_CREDIT_CARD_NAME_INPUT_LABEL" })}
                      minLength={5}
                      maxLength={70}
                      ref={inputRefs.inputCreditCardHolderName}
                    />
                    <div className="brui-flex payment-items-baseline sm:payment-h-44 sm:payment-ml-10 payment-mt-5 sm:payment-mt-7">
                      <LightBoxNoname />
                    </div>
                  </div>
                </FormControl>
                {/* expiration date */}
                <FormControl className="sm:brui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <Label id="label-3" isError={errorExpiryDate} required={true} className={errorExpiryDate ? "payment-error-required" : ""}>
                      {/* {intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_LABEL" })} */}
                      <span id="expiry-month-label" className="brui-sr-only">{Expiration}</span>
                      <span aria-hidden={true}>{intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_LABEL" })}</span>
                    </Label>
                  </div>

                  <div className="brui-flex-col">
                    <FormGroup className="brui-flex brui-flex-wrap" hasError={errorExpiryDate} errorMessage={intl.formatMessage({ id: "ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL" })}
                    >
                      <div className="payment-w-[75px]">
                        <Select
                          name="month"
                          id="select-12"
                          defaultValue=""
                          placeHolder={intl.formatMessage({ id: "CREDIT_CARD_MONTH_PLACEHOLDER" })}
                          onChange={Setselectedmonth}
                          disableDropdownIcon
                          className="brui-text-gray"
                          ref={inputRefs.inputCreditCardExpiryMonth}
                          aria-required={true}
                          aria-labelledby="expiry-month-label"
                        >
                          {getMonthList().map((item, index) => (
                            <SelectOption value={item.toString()} id={"option-" + index} displayName={item.toString()} />
                          ))}
                        </Select>
                      </div>
                      <div className="payment-w-[75px] payment-ml-10">
                        <Select
                          name="year"
                          id="select-2"
                          defaultValue=""
                          placeHolder={intl.formatMessage({ id: "CREDIT_CARD_YEAR_PLACEHOLDER" })}
                          onChange={Setselectedyear}
                          disableDropdownIcon
                          className="brui-text-gray"
                          ref={inputRefs.inputCreditCardExpiryYear}
                          aria-required={true}
                          aria-labelledby="expiry-year-label"
                        >
                          {getYearList().map((item, index: number) => (
                            <SelectOption value={item.toString()} id={"option-year-" + index} displayName={item.toString()} />
                          ))}
                        </Select>
                        {/* <span id="expiry-year-label" className="brui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_SR_YEAR_LABEL" })}</span> */}
                      </div>
                    </FormGroup>
                  </div>
                </FormControl>
                {/* security code */}
                <FormControl className="sm:brui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <Label htmlFor="text-4" isError={errorSecurityCode} required={true} className={errorSecurityCode ? "payment-error-required" : ""}>
                      <span id="cc-security-code-label" className="brui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL" })}</span>
                      <span aria-hidden={true}>{intl.formatMessage({ id: "CREDIT_CARD_SECURITY_CODE_INPUT_LABEL" })}</span>
                    </Label>
                  </div>
                  <div className="brui-flex sm:brui-flex-row brui-flex-wrap">
                    <InputText
                      onInput={(e: any) => numberOnly(e)}
                      className="!payment-w-[75px] payment-mr-10"
                      id="text-4"
                      required={true}
                      isError={errorSecurityCode}
                      errorMessage={intl.formatMessage({ id: "ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL" })}
                      errorMessageClassName="payment-w-[75px] payment-text-nowrap"
                      type="password"
                      onChange={handleMaskCVV}
                      ref={inputRefs.inputCreditCardSecurityCode}
                      aria-labelledby="cc-security-code-label"
                    />
                    <InputText type="hidden" id="text-hidden" value={CVV} />
                    <div className="brui-flex brui-items-center brui-h-44">
                      <LightBoxSecurityCode />
                    </div>
                  </div>
                </FormControl>
              </>
            )}
          </form>
          <Text className="brui-text-14 brui-leading-18 brui-text-gray payment-mt-45 sm:payment-mt-45 brui-inline-block">
            {intl.formatMessage({ id: "REQUIRED_LABEL" })}
          </Text>

          {/* {(isBankChecked || (!isPreauth && !isBankChecked) || (isPreauth && !hasCreditCardDetails)) && (
            <Text className="brui-text-14 brui-leading-18 brui-text-gray payment-mt-45 sm:payment-mt-45 brui-inline-block">
              {intl.formatMessage({ id: "REQUIRED_LABEL" })}
            </Text>
          )} */}
        </div>
      </PaymentRadioCard>
    </div>
  );
});

export const CreditCardPayment = injectIntl(CreditCardPaymentComponent);
