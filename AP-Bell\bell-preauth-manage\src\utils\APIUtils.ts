import { TransactionIdItems, SubscriberOffersWithBan } from "../models";



export function getBanSpecificTransactionId(ban: string, transactionIdArray: TransactionIdItems[]) {
  const transationIdObject = transactionIdArray.filter(a => a.Ban === ban);
  const trasactionID = transationIdObject && transationIdObject[0].TransactionId;
  return trasactionID;
}

export function getCceEmailPayload(ban: string, isBankPaymentSelected: boolean,
  sorryCredit: boolean,
  sorryDebit: boolean,
  debitCardAutopayOffers: SubscriberOffersWithBan[],
  creditCardAutopayOffers: SubscriberOffersWithBan[],
  details?: any[],
) {
  const isDebit = isBankPaymentSelected;
  const isSorry = isDebit ? sorryDebit : sorryCredit;
  const allOffers = isDebit
    ? debitCardAutopayOffers
    : creditCardAutopayOffers;
  const selectedPaymentMethod = isDebit ? "D" : "C";

  if (details && Array.isArray(details)) {
    for (let i = 0; i < details.length; i++) {
      const currentDetail = details[i];

      if (!isSorry) {
        const filteredOffers = allOffers?.filter(
          offer => offer.Ban === currentDetail.accountNumber
        ) ?? [];

        const autopayEligibleSubscribers = filteredOffers
          .reduce((acc, offer) => {
            const subscribers = offer.AutopayEligibleSubscribers ?? [];
            return acc.concat(subscribers);
          }, [] as any[])
          .map(subscriber => ({
            mdn: subscriber.subscriberTelephoneNumber?.replace(/\D/g, ''),
            autopayOffers: subscriber.autopayOffers?.map((offer: any) => ({
              newDiscountAmount: offer.currentdiscountAmount ?? 0,
              currentDiscountAmount: offer.discountAmount ?? 0,
              offerImpact: offer.offerImpact ?? ''
            })) ?? []
          }));

        currentDetail.incentiveDiscountDetails = [
          {
            autopayEligibleSubscribers,
            selectedPaymentMethod
          }
        ];
      } else {
        currentDetail.incentiveDiscountDetails = [];
      }
      return currentDetail;
    }

  }
}
