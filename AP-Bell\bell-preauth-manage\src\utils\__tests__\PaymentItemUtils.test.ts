import { getPaymentItemAccountType } from '../PaymentItemUtils';
import { PaymentItemAccountType } from '../../models';

describe('PaymentItemUtils', () => {
  describe('getPaymentItemAccountType', () => {
    test('should return "My Bill" for Mobility when isNM1 is true', () => {
      const result = getPaymentItemAccountType(PaymentItemAccountType.Mobility, true);
      expect(result).toBe('My Bill');
    });

    test('should return "Mobility" for Mobility when isNM1 is false', () => {
      const result = getPaymentItemAccountType(PaymentItemAccountType.Mobility, false);
      expect(result).toBe('Mobility');
    });

    test('should return "One Bill" for OneBill account type', () => {
      const result = getPaymentItemAccountType(PaymentItemAccountType.OneBill, false);
      expect(result).toBe('One Bill');
    });

    test('should return "TV" for TV account type', () => {
      const result = getPaymentItemAccountType(PaymentItemAccountType.TV, false);
      expect(result).toBe('TV');
    });

    test('should return "Internet" for Internet account type', () => {
      const result = getPaymentItemAccountType(PaymentItemAccountType.Internet, false);
      expect(result).toBe('Internet');
    });

    test('should return "Home Phone" for HomePhone account type', () => {
      const result = getPaymentItemAccountType(PaymentItemAccountType.HomePhone, false);
      expect(result).toBe('Home Phone');
    });

    // Test with isNM1 parameter for non-Mobility types (should not affect result)
    test('should return "One Bill" for OneBill regardless of isNM1 value', () => {
      const resultFalse = getPaymentItemAccountType(PaymentItemAccountType.OneBill, false);
      const resultTrue = getPaymentItemAccountType(PaymentItemAccountType.OneBill, true);
      
      expect(resultFalse).toBe('One Bill');
      expect(resultTrue).toBe('One Bill');
    });
  });
});
