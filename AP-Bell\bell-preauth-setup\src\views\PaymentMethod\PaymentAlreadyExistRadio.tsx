import * as React from "react";
import { RadioButton } from "@bell/bell-ui-library"



export interface PaymentAlreadyExistRadioProps {
  name?: string;
  value: string;
  hasError?: boolean;
  showBankFieldsOnChange?: boolean;
  showOnChange?: (showBankFieldsOnChange: boolean) => void;
  childIndex?: number;
  label: string;
  idPrefix?: string;
  defaultChecked?: boolean;
  className?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
}

export const PaymentAlreadyExistRadio = (
  {
    name = "bank-payment-radio",
    value,
    hasError,
    showOnChange,
    showBankFieldsOnChange = false,
    childIndex,
    defaultChecked,
    className,
    label,
    onChange,
    idPrefix = ""
}:PaymentAlreadyExistRadioProps ) => {
    
  return( 
      <fieldset>
       <RadioButton
      className={[className,"brui-flex brui-items-center brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10"].join(" ").trim()}
      id={idPrefix + "bank-payment-radio-id-" + childIndex}
      name={name}
      value={value}
      variant="boxedInMobile"
      hasError={hasError}
      onChange={() => showOnChange? showOnChange(showBankFieldsOnChange): {onChange}}
      defaultChecked={defaultChecked}
    >
      <div className="brui-text-14 brui-leading-18 brui-mt-3" dangerouslySetInnerHTML={{__html: label}}></div>
    </RadioButton>
      </fieldset>
  )
};
