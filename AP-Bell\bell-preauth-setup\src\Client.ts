import { Injectable, AjaxServices, CommonFeatures, AjaxOptions } from "bwtk";
import { IGetRedirectUrlEpicResponse, SubscriberOffersWithBan, AutopayOffers } from "./models";

import Config from "./Config";

const { BaseClient } = CommonFeatures;

@Injectable
export class Client extends BaseClient {
  constructor(ajax: AjaxServices, private config: Config) {
    super(ajax);
  }

  get options(): AjaxOptions {
    return {
      cache: false,
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        brand: this.config.brand,
        channel: this.config.channel,
        Province: this.config.province,
        userID: this.config.userID,
        "accept-language": this.config.language,
        "X-CSRF-TOKEN": this.config.CSRFToken
      }
    };
  }
   get optionsPMEnabled(): AjaxOptions {
    return {
      cache: false,
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "brand": this.config.brand,
        "channel": this.config.channel,
        "Province": this.config.province,
        "userID": this.config.userID,
        "accept-language": this.config.language,
        "X-CSRF-TOKEN": this.config.CSRFToken,
        "PM": true
      }
    };
  }
  get optionsOneBill(): AjaxOptions {
    return {
      cache: false,
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        brand: this.config.brand,
        channel: this.config.channel,
        Province: this.config.province,
        userID: this.config.userID,
        "accept-language": this.config.language,
        "X-CSRF-TOKEN": this.config.CSRFToken,
        PM: true
      }
    };
  }

  createMultiOrderFormData(ban: string, type?: boolean, details?: any, sub?: string | null) {
    const paymentUrl = type ? this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${null}`) : this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${sub}`);
    const transactionID = this.getBanSpecificTransactionId(ban);
    const url = paymentUrl + `?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill } : { ...this.optionsPMEnabled };
    const response = this.post<any>(url, {
      AccountInputValues: details
    }, ajaxOptions);
    return response;
  }

  validateMultiOrderForm(ban: string, type: boolean, details: any, accountInputValue: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined) {
    const paymentUrl = type ? this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${null}`) : this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${sub}`);
    const transactionID = this.getBanSpecificTransactionId(ban);
    const url = paymentUrl + `/ValidatePayment?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill } : { ...this.optionsPMEnabled };
    const response = this.post<any>(url, isBankPaymentSelected ? {
      AccountInputValues: accountInputValue,
      ValidatePADPACInput: {
        SelectedPaymentMethod: details.SelectedPaymentMethod,
        BankName: details.BankName,
        AccountNumber: details.AccountNumber,
        HolderName: details.HolderName,
        BankCode: details.BankCode,
        TransitCode: details.TransitCode
      }
    } : {
      AccountInputValues: accountInputValue,
      ValidatePADPACInput: {
        SelectedPaymentMethod: details.SelectedPaymentMethod,
        CardholderName: details.CardholderName,
        CreditCardToken: token,
        CreditCardType: details.CreditCardType,
        ExpiryYear: details.ExpiryYear,
        ExpiryMonth: details.ExpiryMonth,
        SecurityCode: details.SecurityCode
      }
    }, ajaxOptions);
    return response;
  }

  submitMultiOrderForm(
    ban: string,
    type: boolean,
    isBankPaymentSelected: boolean,
    sorryCredit: boolean,
    sorryDebit: boolean,
    details?: any[],
    sub?: string | null
  ) {
    const paymentUrl = type
      ? this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${null}`)
      : this.config.createMultiPaymentURL.replace("Onebill", `${ban}/${sub}`);

    const transactionID = this.getBanSpecificTransactionId(ban);
    const url = `${paymentUrl}/Submit?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill } : { ...this.optionsPMEnabled };

    const isDebit = isBankPaymentSelected;
    const isSorry = isDebit ? sorryDebit : sorryCredit;
    const allOffers = isDebit
      ? this.config.debitCardAutopayOffers
      : this.config.creditCardAutopayOffers;
    const selectedPaymentMethod = isDebit ? "D" : "C";

    if (details && Array.isArray(details)) {
      for (let i = 0; i < details.length; i++) {
        const currentDetail = details[i];

        if (!isSorry) {
          const filteredOffers = allOffers?.filter(
            (offer: SubscriberOffersWithBan) => offer.Ban === currentDetail.accountNumber
          ) ?? [];

          const autopayEligibleSubscribers = filteredOffers
            .reduce((acc: any[], offer: SubscriberOffersWithBan) =>
              acc.concat(offer.AutopayEligibleSubscribers ?? []), [])
            .map((subscriber: { subscriberTelephoneNumber: string; autopayOffers: AutopayOffers[]; }) => ({
              mdn: subscriber.subscriberTelephoneNumber?.replace(/\D/g, ''),
              autopayOffers: subscriber.autopayOffers?.map((offer: AutopayOffers) => ({
                newDiscountAmount: offer.currentdiscountAmount ?? 0,
                currentDiscountAmount: offer.discountAmount ?? 0,
                offerImpact: offer.action ?? ''
              })) ?? []
            }));

          currentDetail.incentiveDiscountDetails = [
            {
              autopayEligibleSubscribers,
              selectedPaymentMethod
            }
          ];
        } else {
          currentDetail.incentiveDiscountDetails = [];
        }
      }
    }

    return this.post<any>(
      url,
      {
        AccountInputValues: details
      },
      ajaxOptions
    );
  }

  getPassKeyRepsonse(action: any) {
    const paymentUrl = this.config.paymentApiUrl;
    const url = paymentUrl + `${action.payload.ban}/${action.payload.sub}/payment/CreditCard/PassKey`;
    const ajaxOptions = { ...this.options };
    const response = this.get<any>(url, null, ajaxOptions);
    return response;
  }

  getBanSpecificTransactionId(ban: string) {
    const transationIdObject = this.config.transactionIdArray.filter(a => a.Ban === ban);
    const trasactionID = transationIdObject && transationIdObject[0].TransactionId;
    return trasactionID;
  }

  getRedirectUrl() {
    const url = this.config.RedirectUrl;
    const ajaxOptions = { ...this.options };
    const response = this.post<IGetRedirectUrlEpicResponse>(url, {
      OneTimeCode: "",
      RedirectUrl: this.config.currentUrl
    },
    ajaxOptions);
    return response;
  }

  getInteracBankInfo(code: string) {
    const url = this.config.BankInfoUrl;
    const ajaxOptions = { ...this.options };
    const response = this.post<IGetRedirectUrlEpicResponse>(url, {
      RedirectUrl: this.config.currentUrl,
      OneTimeCode: code
    },
    ajaxOptions);
    return response;
  }

  createOrderFormData(ban: string, type?: boolean, sub?: string | null) {
    const paymentUrl = type ? this.config.createPaymentURL.replace("Onebill", `${ban}`) : this.config.createPaymentURL.replace("Onebill", `${ban}/${sub}`);
    const transactionID = this.getBanSpecificTransactionId(ban);
    const url = paymentUrl + `?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill } : { ...this.options };
    const response = this.post<any>(url, null, ajaxOptions);
    return response;
  }

  validateOrderForm(ban: string, type: boolean, details: any, isBankPaymentSelected: boolean, sub?: string | null, token?: string | null | undefined) {
    const paymentUrl = type ? this.config.createPaymentURL.replace("Onebill", `${ban}`) : this.config.createPaymentURL.replace("Onebill", `${ban}/${sub}`);
    const transactionID = this.getBanSpecificTransactionId(ban);
    const url = paymentUrl + `/ValidatePayment?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill } : { ...this.options };
    const response = this.post<any>(url, isBankPaymentSelected ? {
      SelectedPaymentMethod: details.SelectedPaymentMethod,
      BankName: details.BankName,
      AccountNumber: details.AccountNumber,
      HolderName: details.HolderName,
      BankCode: details.BankCode,
      TransitCode: details.TransitCode
    } : {
      SelectedPaymentMethod: details.SelectedPaymentMethod,
      CardholderName: details.CardholderName,
      CreditCardToken: token,
      CreditCardType: details.CreditCardType,
      ExpiryYear: details.ExpiryYear,
      ExpiryMonth: details.ExpiryMonth,
      SecurityCode: details.SecurityCode
    }, ajaxOptions);
    return response;
  }

  submitOrderForm(ban: string, type: boolean, sub?: string | null) {
    const paymentUrl = type ? this.config.createPaymentURL.replace("Onebill", `${ban}`) : this.config.createPaymentURL.replace("Onebill", `${ban}/${sub}`);
    const transactionID = this.getBanSpecificTransactionId(ban);
    const url = paymentUrl + `/Submit?TransactionId=${transactionID}&province=${this.config.province}`;
    const ajaxOptions = type ? { ...this.optionsOneBill } : { ...this.options };
    const response = this.post<any>(url, null, ajaxOptions);
    return response;
  }

}
