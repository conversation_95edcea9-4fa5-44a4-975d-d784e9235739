
import { Injectable, CommonFeatures, LoggerSeverityLevel } from "bwtk";
import { PaymentItem } from "./models/PaymentItem";
import { DTSTokenization } from "./models/DTSTokenization";
import { SelectListItem, TransactionIdItems, SubscriberOffersWithBan } from "./models";



const { BaseConfig, configProperty } = CommonFeatures;
@Injectable
export default class Config extends BaseConfig<Config> {
  @configProperty("en") 
  language: string;
  // @configProperty("http://127.0.0.1:8881/")
  // url: string;

  // @configProperty("preAuthSetUp")
  // preAuthSetUp: string;

  @configProperty(LoggerSeverityLevel.All)
  logLevel: LoggerSeverityLevel;

  @configProperty("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderForm/PreAuthorizePayment")
  createPaymentURL: string;

  @configProperty("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderMultiForm/PreAuthorizePayment")
  createMultiPaymentURL: string;

  // @configProperty("/UXP.Services/ecare/Ordering/Mobility/:ban/:SubscriberID/OrderForm/PreAuthorizePayment/ValidatePayment?TransactionId=<trasactionID>&province=<province>")
  // validatePaymentURL: string;

  // @configProperty("/UXP.Services/eCare/Ordering/Mobility/:BanNo/:SubNo/OrderForm/PreauthorizePayment/Submit")
  // confirmPaymentURL: string;

  @configProperty("B")
  brand: string;

  @configProperty("BELLCAEXT")
  channel: string;

  @configProperty("ON")
  province: string;

  @configProperty("b14bc3rcGo")
  userID: string;

  @configProperty("")
  CSRFToken: string;

  @configProperty("")
  getPaymentItem: PaymentItem[];

  @configProperty("")
  pagetitle: string;

  @configProperty("")
  DTSTokenization: DTSTokenization;

  @configProperty("")
  paymentApiUrl: string;

  @configProperty("")
  getBankList: SelectListItem[];
  // [{ "TransactionId": "613ca295-3030-468f-8f5e-8317514a4856", "Ban": "2AF62EAA066579DB01C449A81231EC619581CF63FA9AD41EF604A8C2A521C15C7F9E59466C1C3E283AE4241BCA75F21C0C2913A14E9FCF50BEB443782502A468" }, 
  //   { "TransactionId": "90e470f7-3041-4975-b811-b9ff097c18f1", "Ban": "9DB170A6272B12DC0396451EFCA75E99244197A3A5330343EBB4404D05864A07595AD6DE319836AF067E0C77CC35669B9BE159E629834E86C5D24B6DA4A92E4F" }, 
  //   { "TransactionId": "a3b511d4-99ea-4696-bd46-2a588e8b4a4b", "Ban": "E3996E7FF08293CD93E41B400312F94D018A64BAE77377585E43C8A1EECE11B0BE0DA921D230BD307B4EE10BF530C6E6B154C197C24246FEB4A410D2BB060799" }]
  @configProperty("")
  transactionIdArray: TransactionIdItems[];

  @configProperty("")
  RedirectUrl: string;

  @configProperty("")
  BankInfoUrl: string;

  @configProperty("")
  currentUrl: string;

  @configProperty("")
  creditCardAutopayOffers: SubscriberOffersWithBan[];

  @configProperty("")
  debitCardAutopayOffers: SubscriberOffersWithBan[];

  @configProperty("")
  IsInteracEnabled: string;

  @configProperty("")
  IsSingleClickEnabled: string;

  @configProperty("")
  IsAutopayCreditEnabled: string;

  @configProperty("")
  userProfileProvince: string;
  
  @configProperty("/")
  flowInitUrl: string;
}
