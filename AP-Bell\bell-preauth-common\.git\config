[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = https://gitlab.int.bell.ca/uxp/bell-preauth-common.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
[branch "feature-fjsb"]
	remote = origin
	merge = refs/heads/feature-fjsb
	vscode-merge-base = origin/feature-fjsb
