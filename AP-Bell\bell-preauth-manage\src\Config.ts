
import { Injectable, CommonFeatures, LoggerSeverityLevel } from "bwtk";
import { PaymentItem } from "./models/PaymentItem";
import { DTSTokenization } from "./models/DTSTokenization";
import {
  SelectListItem, SubscriberOffersWithBan, TransactionIdItems,
  // SubscriberOffersWithBan 
} from "./models";



const { BaseConfig, configProperty } = CommonFeatures;
@Injectable
export default class Config extends BaseConfig<Config> {
  @configProperty("en")
  language: string;
  // @configProperty("http://127.0.0.1:8881/")
  // url: string;

  // @configProperty("preAuthSetUp")
  // preAuthSetUp: string;

  @configProperty(LoggerSeverityLevel.All)
  logLevel: LoggerSeverityLevel;

  @configProperty("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderForm/PreAuthorizePayment")
  createPaymentURL: string;

  @configProperty("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderMultiForm/PreAuthorizePayment")
  createMultiPaymentURL: string;

  // @configProperty("/UXP.Services/ecare/Ordering/Mobility/:ban/:SubscriberID/OrderForm/PreAuthorizePayment/ValidatePayment?TransactionId=<trasactionID>&province=<province>")
  // validatePaymentURL: string;

  // @configProperty("/UXP.Services/eCare/Ordering/Mobility/:BanNo/:SubNo/OrderForm/PreauthorizePayment/Submit")
  // confirmPaymentURL: string;

  @configProperty("B")
  brand: string;

  @configProperty("BELLCAEXT")
  channel: string;

  @configProperty("ON")
  province: string;

  @configProperty("b14bc3rcGo")
  userID: string;

  @configProperty("")
  CSRFToken: string;

  @configProperty("")
  getPaymentItem: PaymentItem[];

  @configProperty("")
  pagetitle: string;

  @configProperty("")
  DTSTokenization: DTSTokenization;

  @configProperty("")
  paymentApiUrl: string;

  @configProperty("")
  getBankList: SelectListItem[];

  @configProperty("")
  transactionIdArray: TransactionIdItems[];

  @configProperty("")
  RedirectUrl: string;

  @configProperty("")
  BankInfoUrl: string;

  @configProperty("")
  currentUrl: string;

  @configProperty("")
  selectedUpdatePaymentMethod: string;

  @configProperty("")
  isCheckedBan: string;

  @configProperty("")
  creditCardAutopayOffers: SubscriberOffersWithBan[];

  @configProperty("")
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  static language: "en" | "fr";
  // static eligiblePaymentMethods: "D" | "C";


  @configProperty("")
  removedSubscriberOffers: SubscriberOffersWithBan[];

  @configProperty("")
  CancelApiUrl: string;

  @configProperty("")
  IsInteracEnabled: string;

  @configProperty("")
  IsAutopayCreditEnabled: string;

  @configProperty("")
  userProfileProvince: string;

  @configProperty("/")
  flowInitUrl: string;
}
