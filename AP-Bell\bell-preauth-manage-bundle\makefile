build:
	git clone https://gitlab.int.bell.ca/uxp/bell-preauth-manage.git
	cd bell-preauth-manage;git checkout ${CI_COMMIT_REF_NAME} || git checkout Release; rm package-lock.json; ls; npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true 
	git clone https://gitlab.int.bell.ca/uxp/bell-preauth-common.git
	cd bell-preauth-common; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/bell-preauth-manage-bundle.git
	cd bell-preauth-manage-bundle; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true; npm install --package-lock-only --legacy-peer-deps
