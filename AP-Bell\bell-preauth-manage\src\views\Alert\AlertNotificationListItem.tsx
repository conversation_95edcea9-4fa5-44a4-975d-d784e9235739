import React from "react";
import { DivProps } from "@bell/bell-ui-library/dist/@types/src/_types";
import {Text, Link, Price} from "@bell/bell-ui-library";


const variants = {
  priceList: "payment-flex payment-justify-between sm:payment-justify-normal payment-mb-5 last:payment-mb-0",
  errorList: "payment-mb-5 last:payment-mb-0 payment-text-red",
  accountList: "last:payment-mb-0 payment-text-gray payment-p-15 payment-pt-10 sm:payment-pt-15 payment-bg-gray-3 payment-rounded-10",
  accountListSimple: "payment-mb-5 last:payment-mb-0 payment-text-gray",
};

export interface AlertNotificationListItemProps extends DivProps {
  cardDetails?: string,
  label?: string;
  labelDescription?: string | number;
  withPrice?: boolean;
  variant: "priceList" | "errorList" | "accountList" | "accountListSimple" ;
  priceSettings?: {
    language?: "en" | "fr",
    showZeroDecimalPart?: boolean,
    price?: number | null | undefined
  };
  inputRef?: React.RefObject<HTMLInputElement> | React.RefObject<HTMLSelectElement>;
}

export const AlertNotificationListItem = (
  { 
    cardDetails,
    label, 
    labelDescription, 
    priceSettings = {
      showZeroDecimalPart: true,
      price: 0.00
    }, 
    variant, 
    className, 
    children, 
    inputRef,
    ...rest 

  }: AlertNotificationListItemProps) => {
  const srText = label + " - " + labelDescription;

  const handleOnClick = () => {

    if(inputRef?.current instanceof HTMLSelectElement) {
      let sibling = inputRef?.current?.previousElementSibling as HTMLButtonElement;
      while (sibling) {
        if (sibling.tagName === 'BUTTON') {
          const button = sibling as HTMLButtonElement;
          scrollToElement(button);
          button.focus();
          return;
        }
        sibling = sibling.previousElementSibling as HTMLButtonElement;
      }
    } else {
      scrollToElement(inputRef?.current);
      inputRef?.current?.focus();
    }
  };

  const scrollToElement = (el: HTMLButtonElement | HTMLInputElement | undefined | null) => {
    if (el) {
      el.scrollIntoView({
        behavior: 'smooth',
        block: 'center', 
      });
    }
  };

  return (
    <div role="listitem"
      className={["",variants[variant],className].join(" ").trim()}
      {...rest}
    >
      {variant === "priceList" &&
        <>
          <Text className="payment-text-14 payment-leading-18 sm:payment-min-w-[153px]">
            <strong>{label}</strong>
          </Text>
          <Price 
            language={priceSettings.language ? priceSettings.language: "en"}
            showZeroDecimalPart={priceSettings.showZeroDecimalPart}
            price={typeof labelDescription === "number" ? labelDescription : 0.00}
            variant="defaultPrice"
            className="!payment-text-14 payment-leading-18  payment-font-normal"
          /> 
        </>   
      } 

      {variant === "errorList" &&
        <>
          <span className="payment-text-14" aria-hidden="true">•</span>
          <Link variant="textRed" size="small" href="javascript:void(0)" aria-label={srText} className="payment-font-bold payment-ml-5" onClick={handleOnClick}>{label} </Link>
          <span className="payment-text-gray payment-text-14"> - {labelDescription}</span>
        </>
      }

      {variant === "accountList" &&
        <div className="payment-flex payment-flex-wrap payment-justify-between">
          <Text className="payment-mr-5 payment-mt-5 sm:payment-mt-0" aria-hidden="true">
            <strong className="payment-text-black">{label}</strong>&nbsp;{labelDescription}
          </Text>
          <span className="payment-sr-only">{labelDescription}</span>
          <Text elementType="span" className="payment-mt-5 sm:payment-mt-0">
            {cardDetails}
          </Text>
        </div>
      }

      {variant === "accountListSimple" &&
        <div className="payment-flex payment-flex-wrap payment-justify-between">
          <Text className="payment-mr-5 payment-mt-5 sm:payment-mt-0">
            <strong className="payment-text-black">{label}</strong>&nbsp;{labelDescription}
          </Text>
          <Text elementType="span" className="payment-mt-5 sm:payment-mt-0">
            {cardDetails}
          </Text>
        </div>
      }
    </div>
  );
};

export default AlertNotificationListItem;
