import * as React from "react";
import { RadioButton } from "@bell/bell-ui-library"



export interface BankPaymentRadioButtonProps {
  name?: string;
  value: string;
  hasError?: boolean;
  showBankFieldsOnChange?: boolean;
  showOnChange?: (showBankFieldsOnChange: boolean) => void;
  childIndex?: number;
  label: string;
  idPrefix?: string;
  defaultChecked?: boolean;
  className?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
}

export const FormFieldsBankRadioPayment = (
  {
    name = "bank-payment-radio",
    value,
    hasError,
    showOnChange,
    showBankFieldsOnChange = false,
    childIndex,
    defaultChecked,
    className,
    label,
    onChange,
    idPrefix = ""
}:BankPaymentRadioButtonProps ) => {
    
  return( 
      <fieldset>
       <RadioButton
      className={[className,"brui-flex brui-items-center"].join(" ").trim()}
      id={idPrefix + "bank-payment-radio-id-" + childIndex}
      name={name}
      value={value}
      variant="boxedInMobile"
      hasError={hasError}
      onChange={() => showOnChange? showOnChange(showBankFieldsOnChange): {onChange}}
      defaultChecked={defaultChecked}
    >
      <div className="brui-text-14 brui-leading-18 brui-mt-3" dangerouslySetInnerHTML={{__html: label}}></div>
    </RadioButton>
      </fieldset>
  )
};
