export const numberOnly = (e: any) => {
  e.target.value = e.target.value.replace(/[^0-9]/gi, "");
};

export const getMonthList = () => Array.from({ length: 12 }, (_, i) => {
  let month: string | number = i + 1;
  if (i < 9) {
    month = i + 1;
    month = "0" + month;
  }
  return month;
});

export const getYearList = () => {
  const max = parseInt(new Date().getFullYear().toString().substring(2));
  const min = max + 9;
  const years = [];
  for (let i = max; i <= min; i++) {
    years.push(i);
  }
  return years;   
};
