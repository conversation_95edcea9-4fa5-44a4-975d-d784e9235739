import React, { forwardRef } from "react";
import {CheckboxCard,Text,Price, Icon } from "@bell/bell-ui-library";

import { PaymentItem } from "../../models";

export interface BillProps extends React.ComponentPropsWithoutRef<"div">{
  isDisabled?: boolean;
  isChecked?: boolean;
  label?: string;
  id?: string;
  billType?: string;
  billAccountNumber?: string;
  idIndex?: number;
  text?: React.ReactNode;
  priceSettings?: {
    price: number;
    language?: "en" | "fr";
    negativeIndicator?: "CR" | "-" | null;
   
  };
  item: PaymentItem;
  isCheckedItems: PaymentItem[];
  setIsCheckedItems: Function;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isShowLabel?: boolean;
  paymentItems: PaymentItem[];
  intl: any; 
}


export const CheckboxCardBill = forwardRef<HTMLInputElement, BillProps>(({
  className,
  isDisabled,
  isChecked,
  label,
  id,
  billType,
  billAccountNumber,
  idIndex,
  text,
  priceSettings,
  item,
  isCheckedItems,
  setIsCheckedItems,
  onChange,
  isShowLabel,
  paymentItems,
  intl
}: BillProps, ref) => {
  const [isCheckBoxChecked, setCheckBoxChecked] =  React.useState<PaymentItem[]>([]);
  const ban = item.Ban;
  const handleCheckboxChange = (event: any, item: PaymentItem) => {
    if (event.target.checked)
    {
      setIsCheckedItems([...isCheckedItems, item]);
    }
    else
    {
      setIsCheckedItems((isCheckedItems: any) => isCheckedItems.filter((checkedItem: any) => checkedItem.BillName !== item.BillName));
    }
  };

  React.useEffect(() => {
    const inputBoxes = document.querySelectorAll("input[type='checkbox']");
    const checkedBoxes: any[] = [];
    inputBoxes.forEach(a => {
      if ((a as HTMLInputElement).checked) {
        const dataAttribute = a.getAttribute("data-bandetail");
        const dataChecked = dataAttribute && JSON.parse(dataAttribute);
        const checkedBan = dataChecked && dataChecked.ban && paymentItems.filter(item => item.Ban === dataChecked.ban);
        if(checkedBan && checkedBan.length > 0)
        {
          checkedBoxes.push(checkedBan[0]);
        }
      }
    });
    if (checkedBoxes && checkedBoxes.length > 0 && checkedBoxes.map(item => item !== null)){
      const checkedCheckBoxes = checkedBoxes.filter(item => item !== null);
      setCheckBoxChecked((isCheckedItems: any) => isCheckedItems.concat([...checkedCheckBoxes]));
    }
  }, []);

  React.useEffect(() => {
    if (isCheckBoxChecked !== null && isCheckBoxChecked.length > 0) {
      const uniqueitems = isCheckBoxChecked.reduce((acc: PaymentItem[], cur) => {
        const existingItem = acc.find((item: PaymentItem) => cur.Ban === item.Ban);
        if (!existingItem) {
          acc.push({ ...cur });
        }
        return acc;
      }, []);
      
      setIsCheckedItems(uniqueitems);
    }

  }, [isCheckBoxChecked]);

  return (
    <CheckboxCard
      ref={ref}
      id={id}
      aria-labelledby={label}
      disabled={isDisabled}
      defaultChecked={isChecked}
      className={["check-manage group-has-[:disabled]/inputcheckbox:payment-bg-gray-3 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-30 payment-pl-30 brui-pr-30 payment-pb-40 sm:payment-pb-30 sm:payment-p30 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 lg:payment-basis-1/3 brui-w-full payment-mr-15 brui-mb-15", className].join(" ").trim()}
      defaultPadding={false}
      checkboxPlacement="topLeft"
      data-banDetail={JSON.stringify({id,billAccountNumber, ban, billType, price: priceSettings?.price})}
      onChange={(e) => handleCheckboxChange(e, item)}
    >
      <div className="sm:brui-flex payment-pl-[25px] sm:payment-pl-[35px] payment-pr-[15px] sm:payment-pr-[30px] payment-flex-col payment-relative payment-top-[5px] sm:payment-top-0 sm:payment-min-h-[48px]">
        <div
          className="brui-flex brui-w-max brui-items-center"
          id={`checkboxBill${idIndex}-label-${idIndex}`}
        >
          <Text elementType="span" className="brui-font-bold">
            {billType}{" "}
            <Text elementType="span" className="brui-text-14 brui-text-gray !payment-font-normal">
              {billAccountNumber}
            </Text>
          </Text>
        </div>
        {isShowLabel && (
          <div className="brui-flex brui-w-fit brui-items-center payment-mr-[15px]" id={`checkboxBillBalance-${idIndex}-label-${idIndex}`}>
            <Text
              elementType="span"
              className="brui-text-14 brui-text-gray brui-break-words brui-flex brui-items-center brui-leading-18"
            >
              {text} 
            </Text>
            {priceSettings && (
              <Price
                language={priceSettings.language ? priceSettings.language : "en"}
                negativeIndicator={priceSettings.negativeIndicator ? priceSettings.negativeIndicator : "CR"}
                price={priceSettings.price ? priceSettings.price : 0.0}
                variant="defaultPrice"
                className="!payment-text-14 brui-leading-14 payment-m-5"
              />
            )}
          </div>
        )}

        <div className="payment-flex payment-w-fit payment-items-top" id={`checkboxBill${idIndex}-label-${idIndex}`}>
          {!isShowLabel && (
            <>
              <Icon
                className="payment-text-yellow payment-mr-5"
                iconClass="bi_brui"
                iconName="bi_error_bl_bg_cf"
              />
              <Text
                elementType="span"
                className="payment-text-14 payment-text-gray payment-break-words payment-flex payment-items-center payment-leading-18">
                {intl.formatMessage({id: "NOT_ON_PREAUTH"})}
              </Text>
            </>
          )}
        </div>
      </div>
    </CheckboxCard>
  );
}
);

