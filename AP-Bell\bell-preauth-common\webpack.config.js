const { libTemplate } = require("webpack-common");
const package = require("./package.json");
const path = require("path");
const webpack = require("webpack");


module.exports = (env) => {
  const configs = libTemplate(package, {
    lib: path.resolve(__dirname, "src"),
    node_modules: path.resolve(__dirname, "node_modules"),
    dist: path.resolve(__dirname, "dist"),
  }, {});

  // If multiple configs are returned, we need to handle DefinePlugin conflicts
  if (Array.isArray(configs) && configs.length > 1) {
    configs.forEach((config, index) => {
      // Remove existing DefinePlugin instances to avoid conflicts
      config.plugins = config.plugins.filter(plugin => !(plugin instanceof webpack.DefinePlugin));

      // Add a single DefinePlugin with the appropriate NODE_ENV
      const nodeEnv = config.mode === 'production' ? 'production' : 'development';
      config.plugins.push(new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(nodeEnv)
      }));
    });
  }

  return configs;
};

